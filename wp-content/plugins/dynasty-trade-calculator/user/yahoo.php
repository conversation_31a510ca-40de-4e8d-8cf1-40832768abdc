<?php 

new  yahoo_api;

class yahoo_api {
	protected $slug;
	protected $name;
	protected $username;
	protected $endpoint;
	protected $season;	
	protected $userId;
	protected $leagueTeams;
	protected $rounds;
	protected $includeDraftPicks;
	
	public function __construct(){
		$this->slug = 'yahoo';
		$this->name = 'Yahoo';
		$this->username = 'Yahoo';
		$settings = get_option('dtc_settings');	
		$this->endpoint =$settings[''.$this->slug.'_url'];
		$this->season =$settings[''.$this->slug.'_season'];

		$this->rounds = 4;

		$this->includeDraftPicks = true;

		$this->leagueTeams = [];

		add_action('dtc/admin/settings',array($this,'settings'));
		add_action('dtc/admin/settings/api_settings',array($this,'api_settings'));
		add_action('dtc_calculator_bottom', array($this,'view'));
		add_action('dtc/league_import/content',array($this,'import_content'));
		add_action('dtc/league_import/button',array($this,'import_button'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_league_settings', array($this,'ajax_get_league_settings'));	

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_opposing_players_dropdown', array($this,'ajax_get_opposing_players_dropdown'));	
		add_action( 'wp_ajax_nopriv_dtc_'.$this->slug.'_ajax_get_opposing_players_dropdown', array($this,'ajax_get_opposing_players_dropdown'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_league_players', array($this,'ajax_get_league_players'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_get_league_players', array($this,'ajax_get_league_players'));
		
		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_login', array($this,'ajax_login'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_login', array($this,'ajax_login'));
		
		add_action( 'wp_ajax_dtc_'.$this->slug.'_ajax_logout', array($this,'ajax_logout'));	
		add_action( 'wp_ajax_nopriv_dtc_'.$this->slug.'_ajax_logout', array($this,'ajax_logout'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_main_loader', array($this,'ajax_main_loader'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_main_loader', array($this,'ajax_main_loader'));

		add_action('wp_ajax_yahoo_login', array($this,'yahooLogin'));
		add_action('wp_ajax_nopriv_yahoo_login', array($this,'yahooLogin'));

		add_action('wp_ajax_process_auth_code', array($this,'process_auth_code'));
		add_action('wp_ajax_nopriv_process_auth_code', array($this,'process_auth_code'));
	}

	/* OAuth Functions */
	function getYahooAuthUrl() {
		$clientId = 'dj0yJmk9alhLSWdDSjJHOUJ1JmQ9WVdrOVJuQTBZa1JaVlVNbWNHbzlNQT09JnM9Y29uc3VtZXJzZWNyZXQmc3Y9MCZ4PTMx';
		$redirectUri = 'oob';
	
		$authUrl = 'https://api.login.yahoo.com/oauth2/request_auth';
		$params = http_build_query([
			'client_id' => $clientId,
			'redirect_uri' => $redirectUri,
			'response_type' => 'code',
			'state' => bin2hex(random_bytes(8))
		]);
	
		return $authUrl . '?' . $params;
	}

	function loginWindow() {
		?>
		<script type="text/javascript">
			if (typeof authUrl === 'undefined') {
				let authUrl = '';
			}

			jQuery(document).ready(function($) {
				function fetchYahooAuthUrl() {
					fetch('<?php echo admin_url('admin-ajax.php?action=yahoo_login'); ?>')
						.then(response => response.json())
						.then(data => {
							authUrl = data.authUrl;

							document.getElementById('yahoo-login-area').style.display = 'block';
							document.getElementById('yahoo-loading-area').style.display = 'none';
						})
						.catch(error => {
							console.error('Error fetching Yahoo login URL:', error);
						});
				}

				fetchYahooAuthUrl();
			});

			function openYahooLoginTab() {
				if (authUrl) {
					window.open(authUrl, '_blank');
					
				} else {
					console.error('Yahoo Auth URL not available.');
				}
			}
		</script>
		<?php
	}

	function yahooLogin() {
		$authUrl = $this->getYahooAuthUrl();
	
		wp_send_json(['authUrl' => $authUrl]);
	}

	private function getAccessToken($authCode) {
		$tokenUrl = 'https://api.login.yahoo.com/oauth2/get_token';
		$clientId = 'dj0yJmk9alhLSWdDSjJHOUJ1JmQ9WVdrOVJuQTBZa1JaVlVNbWNHbzlNQT09JnM9Y29uc3VtZXJzZWNyZXQmc3Y9MCZ4PTMx';
		$clientSecret = '212cafaf971635d670cabd3d579cd53424a652f8';
		$redirectUri = 'oob';

		// Prepare the data for the POST request
		$data = [
			'client_id' => $clientId,
			'client_secret' => $clientSecret, // Add your client secret
			'redirect_uri' => $redirectUri,
			'code' => $authCode,
			'grant_type' => 'authorization_code'
		];
	
		// Set up the request
		$response = wp_remote_post($tokenUrl, [
			'body' => http_build_query($data),
			'headers' => [
				'Content-Type' => 'application/x-www-form-urlencoded',
			]
		]);

		// Handle the response
		if (is_wp_error($response)) {
			// Log the error or handle it appropriately
			// $error_message = $response->get_error_message();
    		// echo "Yahoo Error: $error_message";
			// return false;

			$error_message = $response->get_error_message();
			$result = ['error_message' => "Yahoo Error: $error_message"];
			return $result;
		}
	
		// Decode the response body
		$body = json_decode(wp_remote_retrieve_body($response), true);
	
		// Check for access token
		if (isset($body['access_token'])) {
			$accessToken = $body['access_token'];
			$refreshToken = $body['refresh_token'];
			$expiresIn = $body['expires_in'];

			$this->store_yahoo_tokens($accessToken, $refreshToken, $expiresIn);

			return $body;
		}
		else {
			if (isset($body['error_description'])) {
				$result = ['error_message' => $body['error_description']];
			} else {
				$result = ['error_message' => json_encode($body)];
			}
		}
	
		return $result;
	}

	function refresh_yahoo_token($refresh_token) {
		// App ID - Fp4bDYUC
		$client_id = 'dj0yJmk9alhLSWdDSjJHOUJ1JmQ9WVdrOVJuQTBZa1JaVlVNbWNHbzlNQT09JnM9Y29uc3VtZXJzZWNyZXQmc3Y9MCZ4PTMx';
		$client_secret = '212cafaf971635d670cabd3d579cd53424a652f8';
		$token_endpoint = 'https://api.login.yahoo.com/oauth2/get_token';
	
		$response = wp_remote_post($token_endpoint, [
			'body' => [
				'client_id' => $client_id,
				'client_secret' => $client_secret,
				'refresh_token' => $refresh_token,
				'grant_type' => 'refresh_token'
			]
		]);

		if (!is_wp_error($response)) {
			$body = wp_remote_retrieve_body($response);
			$new_token_data = json_decode($body, true);
	
			if (isset($new_token_data['access_token'])) {
				$accessToken = $new_token_data['access_token'];
				$refreshToken = $new_token_data['refresh_token'];
				$expiresIn = $new_token_data['expires_in'];

				$this->store_yahoo_tokens($accessToken, $refreshToken, $expiresIn);

				return $new_token_data; // Contains new access and refresh tokens
			}
		}
	
		return null; // Handle errors as needed
	}

	function store_yahoo_tokens($access_token, $refresh_token, $expires_in) {
		$user_id = get_current_user_id();

		update_user_meta($user_id, 'yahoo_access_token', $access_token);
		update_user_meta($user_id, 'yahoo_refresh_token', $refresh_token);
		update_user_meta($user_id, 'yahoo_token_expires', time() + $expires_in);
	}
	
	function get_valid_yahoo_token() {
		$user_id = get_current_user_id();

		$access_token = get_user_meta($user_id, 'yahoo_access_token', true);
		$refresh_token = get_user_meta($user_id, 'yahoo_refresh_token', true);
		$expires = get_user_meta($user_id, 'yahoo_token_expires', true);

		if (time() < $expires) {
			// Token is still valid
			return $access_token;
		}
	
		// Token expired, use refresh token to get a new one
		$new_tokens = $this->refresh_yahoo_token($refresh_token);
	
		if ($new_tokens) {
			$this->store_yahoo_tokens($new_tokens['access_token'], $new_tokens['refresh_token'], $new_tokens['expires_in']);
			return $new_tokens['access_token'];
		}
	
		return null; // Handle token refresh failure
	}

	function process_auth_code() {
		// Check the nonce for security (optional)
		// if (!isset($_POST['yahoo_auth_nonce']) || !wp_verify_nonce($_POST['yahoo_auth_nonce'], 'yahoo_auth_nonce')) {
		//     wp_send_json_error('Nonce verification failed!');
		// }
	
		if (isset($_POST['auth_code'])) {
			$auth_code = sanitize_text_field($_POST['auth_code']); // Sanitize the input
			
			// Call your function to process the auth code
			$result = $this->getAccessToken($auth_code); // Adjust this to fit your method
	
			// Return success or failure message
			if (isset($result['error_message']) && !empty($result['error_message'])) {
				wp_send_json_error($result['error_message']);
			} else {
				wp_send_json_success('Access token retrieved successfully!');
			}
		} else {
			wp_send_json_error('No auth code provided.');
		}
	
		wp_die(); // Required to properly terminate AJAX requests in WordPress
	}
	


	
	/* Yahoo Basic Integration Functions */

	function ajax_main_loader() {
		global $current_user;
		$user_id = $current_user->ID;

		$access_token = $this->get_valid_yahoo_token();

		if (!$access_token) {
			$auth_url = $this->loginWindow();
			$plugin_base_url = plugin_dir_url(__DIR__);
			?>
			<div class="dtc-mfl-nav yahoo-login">
				<div>
					<div style="text-align: center;"><img style="max-width: 40%;" src="<?php echo $plugin_base_url; ?>/asetts/images/api/yahoo-logo.png"></div>
					<h1 id="yahoo-login-text">Login</h1>
				</div>
				<div id="yahoo-login-area" style="display:none;">
					<div style="width: 400px; margin: 20px auto; padding: 20px; background: white; text-align: center;">
						<!--<p>Click the button below to log in with Yahoo.<br/>This will open a new tab.<br/>When you have completed your registration then paste your authorization code below.</p>-->
						<label class="yahoo-label" for="auth_code">Step 1</label>
						<button onclick="openYahooLoginTab()" id="yahoo-login-button">Get Code</button>
					</div>

					<div style="display: flex; justify-content: center; align-items: center; width: 100%;">
						<form id="auth-form" style="max-width: 400px; width: 100%; text-align: center;">
							<label class="yahoo-label" for="auth_code">Step 2</label>
							<input type="text" id="auth_code" name="auth_code" placeholder="Enter Code" required />
							<button id="submit_auth_code" type="button">Submit</button>
							<div id="yahoo-login-overlay" style="display: none;"><div class="dtc-mfl-overlay-wrap"><img src="<?php echo DTC_URL; ?>/asetts/images/loading.gif"></div></div>
						</form>
					</div>
				</div>
				<div id="yahoo-loading-area"  style="display:block; text-align: center; color:#000000;">
					Loading . . .
					<br/>Please wait
				</div>
				<script type="text/javascript">
					jQuery(document).ready(function($) {
						$('#submit_auth_code').on('click.authForm', function(event) {
							event.preventDefault(); // Prevent the default form submission

							document.getElementById('submit_auth_code').style.display = 'none';
							document.getElementById('yahoo-login-overlay').style.display = 'block';

							var authCode = $('#auth_code').val(); // Get the auth code
							var data = {
								action: 'process_auth_code', // Your custom action
								auth_code: authCode
							};

							// Send the AJAX request
							$.post('<?php echo admin_url('admin-ajax.php?action=process_auth_code'); ?>', data, function(response) {
								if (response.success === true) {
									localStorage.setItem('dtc_integration_name', 'yahoo');
									window.IntegrationName = "yahoo";
									window.rotoGPT_LeagueIntegrationName = "yahoo";
									window.rotoGPT_LeagueUserId = undefined;
									window.rotoGPT_LeagueId = undefined;
									window.rotoGPT_LeagueName = undefined;
									window.rotoGPT_UserEmail = undefined;

									dtc_refresh_main_loader();
								} else {
									document.getElementById('submit_auth_code').style.display = 'block';
									document.getElementById('yahoo-login-overlay').style.display = 'none';

									// Need to think about what to do here!
									alert(response.data);
								}
							});
						});
					});


				</script>
			</div>
			<?php

		} else {
			echo '	
			<h1>'.$this->name.'</h1>
			<div class="dtc-mfl-nav">
				<a href="#" class="'.$this->slug.'-refresh-data mfl-button" data-id="'.$current_user->ID.'">Refresh Data</a>
				<a href="#" class="'.$this->slug.'-logout mfl-button" data-id="'.$current_user->ID.'">Logout</a>
			</div>
			';
	
			$leagues = $this->get_leagues();
			# echo print_r($leagues);

			echo '<div class="dtc-mfl-half">';
			echo '	<div class="dtc-half-inner">';

			$name = 'yahoo_your_team';
			$html = '<select name="'.$name.'" class="'.$name.' league_dropdown"><option value="">Your Team</option>';
		
			foreach ($leagues as $league) {
				$html .= '<option value="'.$league->leagueId.'" data-my-team-id="'.$league->teamId.'" data-my-team-name="'.$league->teamName.'">'.$league->leagueName.' -  '.$league->teamName.'</option>';
			}

			$html .='</select>';

			$html .= '
				<script>
					jQuery(document).ready(function(){
						var league_id = localStorage.getItem("dtc_league_id");
						
						if (league_id) {
							jQuery("select[name=\'yahoo_your_team\'] option[value=\'" + league_id + "\']").prop("selected", true);
							jQuery("select[name=\'yahoo_your_team\']").trigger("change");
						}
					});
				</script>
				';

			echo $html;

			echo '
				</div>
			</div>
			';

			echo '
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
			';
			echo '<div class="'.$this->slug.'-opposing-teams"><select disabled="disabled" ><option value="">Your Competitor</option></select></div>';
			echo '
				</div>
			</div>
			
			<div style="clear:both"></div>
			
			<div class="mfl-import-calc">
				<a href="#" class="mfl-import-calc-button">Import to Calc</a>
			</div>
			
			<div class="'.$this->slug.'-league-settings"></div>
			
			<div class="mfl-team-gutter">
				<div class="dtc-mfl-half"><div class="dtc-half-inner">
				<div class="'.$this->slug.'-import-team-one"></div>
			
				</div>
			</div>
			
			<div class="dtc-mfl-half">
				<div class="dtc-half-inner">
					<div class="'.$this->slug.'-import-team-two">
			
					</div>
				</div>
			</div>
			
			<div style="clear:both"></div>
			';
		}

		die();
	}

	function ajax_login() {
		$this->request_token();	
		die();
	}

	function ajax_logout() {
		$user_id = get_current_user_id();

		delete_user_meta($user_id, 'yahoo_access_token');
		delete_user_meta($user_id, 'yahoo_refresh_token');
		delete_user_meta($user_id, 'yahoo_token_expires');

		echo $this->ajax_main_loader();
	}

	function get_token() {
		$access_token = $this->get_valid_yahoo_token();

		if (!$access_token) {
			return null;
		}
		
		return $access_token;
	}

	function request_token() {

		global $current_user;
		
		echo $this->ajax_main_loader();
	}

	function is_active() {

		$settings = get_option('dtc_settings');	
		
		if ($settings[''.$this->slug.'_display']== 0) {
			return false;	
		}

		if ($settings[''.$this->slug.'_display']== 1 && !current_user_can('manage_options') ) {	
			return false;	
		}
			
		return true;
	}

	function import_content() {
		if ($this->is_active() == true) {

			$this->javascript_functions();
			?>

			<style type="text/css">
				.<?php echo $this->slug; ?>-loader select{width:100%;}
				.<?php echo $this->slug; ?>-loader {color:#c3c6ca;}
				.<?php echo $this->slug; ?>-loader h1{color:#c3c6ca;}
				.<?php echo $this->slug; ?>-login{background-color: #FFF;
				color: #fbfbfb;}
				.<?php echo $this->slug; ?>-login{min-height:500px;padding:20px 0px;}
				.<?php echo $this->slug; ?>-login input[type=text]{width:80%;border-radius:5px;}
				.<?php echo $this->slug; ?>-login  .dtc-button{-moz-border-radius: 5px;
					border-radius: 26px;
					height: 52px;
					font-size: 14px;
					color: #FFF;
					background-color: #333;
					text-align:center;
					font-weight:600;
					min-width:100px;
				}
			</style>
			<div class="<?php echo $this->slug; ?>-loader league-import-item">
				
			</div>
			<?php
		}
	}

	function import_button() {
		if ($this->is_active()  == true) {
				echo '<a href="" class="dtc-refresh-main-loader '.$this->slug.'-loader-button" data-id="'.$this->slug.'">'.$this->username.'</a>';
		}
	}

	public function view() {
		
	}

	function api_settings($settings) {
		echo ' 
		<tr>
			<td style="width:150px !important">'.$this->name.' URL</td>
			<td><input type="text" name="dtc_settings['.$this->slug.'_url]" value="'.$settings[''.$this->slug.'_url'].'" style="width:300px;max-width:none"> </td>
		</tr>
		<tr>
			<td style="width:150px !important">'.$this->name.' Season</td>
			<td><input type="text" name="dtc_settings['.$this->slug.'_season]" value="'.$settings[''.$this->slug.'_season'].'" style="width:300px;max-width:none"> </td>
		</tr>
		';
	}

	function ajax_get_league_settings() {
		global $current_user;

		$league_id = $_POST['league_id'] ?? '';

		if ($league_id == '') {
			die();
		}

		$leagueInfo = $this->getSettingsDataForLeague($league_id);
		$token = $this->get_token();

		?>
			<script type="text/javascript">
				localStorage.setItem('dtc_integration_name', 'yahoo');
				localStorage.setItem('dtc_league_name', '<?php echo $leagueInfo->league_name; ?>');
				localStorage.setItem('dtc_league_id', '<?php echo $league_id; ?>');

				// Populating this so we don't have conflicting uses of rotoGPT and DTC
				window.IntegrationName = "yahoo";
				window.IntegrationLeagueName = "<?php echo $leagueInfo->league_name; ?>";
				window.IntegrationLeagueId = "<?php echo $league_id; ?>";

				window.rotoGPT_LeagueIntegrationName = "yahoo";
				window.rotoGPT_LeagueToken = "<?php echo $token; ?>";
				window.rotoGPT_LeagueId = "<?php echo $league_id; ?>";
				window.rotoGPT_LeagueName = "<?php echo $leagueInfo->league_name; ?>";

				var event = new Event('IntegrationLeagueNameChanged');
				window.dispatchEvent(event);
			</script>
		<?php

		
		$rules_array['team_type'] = $leagueInfo->league_format;
		
		if ($leagueInfo->settings['superflex']) {
			$rules_array['team_format'] = 'SF';
		} else if ($leagueInfo->settings['2QB']) {
			$rules_array['team_format'] = '2QB';
		} else {
			$rules_array['team_format'] = 'STANDARD';
		}

		$rules_array['team_size'] = $leagueInfo->league_size;

		if ($leagueInfo->settings['TE_Premium']) {
			$rules_array['tepre'] = 'YES';
		} else {
			$rules_array['tepre'] = 'NO';
		}

		if ($leagueInfo->settings['RB_PPC']) {
			$rules_array['rbppc'] = 'YES';
		} else {
			$rules_array['rbppc'] = 'NO';
		}

		echo '	
		<input type="hidden" class="mfl-rule-team-type" value="'.$rules_array['team_type'].'">
		<input type="hidden" class="mfl-rule-team-format" value="'.$rules_array['team_format'].'">
		<input type="hidden" class="mfl-rule-team-size" value="'.$rules_array['team_size'].'">
		<input type="hidden" class="mfl-rule-team-tepre" value="'.$rules_array['tepre'].'">
		<input type="hidden" class="mfl-rule-team-rbppc" value="'.$rules_array['rbppc'].'">';

		echo '
		<div class="mfl-league_settings">
			<span><strong>Type: </strong>'.$rules_array['team_type'].'</span>
			<span><strong>Format: </strong>'.$rules_array['team_format'].'</span>
			<span><strong>Size: </strong>'.$rules_array['team_size'].'tm</span>
			<span><strong>TE Premium? </strong>'.$rules_array['tepre'].'</span>
			<span><strong>RB PPC? </strong>'.$rules_array['rbppc'].'</span>
		</div>';

		?>
			<script type="text/javascript">
				_dtc_save_setting('ls', "<?php echo $rules_array['team_size']; ?>");

				if ("<?php echo $rules_array['team_type']; ?>" == "PPR") {
					_dtc_save_setting('lt', "ppr");
				} else if ("<?php echo $rules_array['team_type']; ?>" == "HALF PPR") {
					_dtc_save_setting('lt', "half_ppr");
				} else if ("<?php echo $rules_array['team_type']; ?>" == "NON PPR") {
					_dtc_save_setting('lt', "nonppr");
				}
				
				_dtc_save_setting('lf', "<?php echo strtolower($rules_array['team_format']); ?>");

				var lfa_settings = '';

				if ("<?php echo $rules_array['tepre']; ?>" == "YES") {
					lfa_settings += 'te-premium,';
				}

				if ("<?php echo $rules_array['rbppc']; ?>" == "YES") {
					lfa_settings += 'rb-ppc-premium,';
				}

				lfa_settings += 'offense,';

				_dtc_save_setting('lfa',lfa_settings);

				recalculatePlayers();
			</script>
		<?php

		die();
	}

	function ajax_get_opposing_players_dropdown() {
		global $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$team_id = $_POST['my_team_id'] ?? '';
		
		// $login_cookie =  get_user_meta($current_user->ID,'_'.$this->slug.'_login_cookie',true);
		// $league = $this->get_leauge($league_id);
		
		$class_name = $this->slug . '_load_opposing_team';

		$teams = $this->get_opponents($league_id,$team_id);	
		
		if (!empty($teams)) {
			$firstTeam = $teams[0];
		
			$leagueId = $firstTeam->leagueId;
			$leagueName = $firstTeam->leagueName;

			$league_name = ! empty( $leagueName ) ? $leagueName : '';
			$html = '<select name="'.$class_name.'" class="'.$class_name.'" data-id="'.$leagueId.'" data-name="'.$league_name.'"><option value="">Your Competitor</option>';
			
			foreach($teams as $team) {
				if ($team->teamId !== $team_id) {
					$html .= '<option value="'.$team->teamId.'" data-my-team-name="'.$team->teamName.'" >'.$team->teamName.'</option>';				
				}
			}
			
			$html .='</select>';
			
			echo $html;
		}

		die();
	}

	function settings($settings) {
		$selected0 = '';
		$selected1 = '';
		$selected2 = '';

		echo '  
		<tr>
			<td style="width:150px">'.$this->name.' Display</td>
			<td>';
			if ($settings[''.$this->slug.'_display'] == 0) {
				$selected0 ='selected="selected"'; 
			}

			if ($settings[''.$this->slug.'_display'] == 1) {
				$selected1 ='selected="selected"'; 
			}

			if ($settings[''.$this->slug.'_display'] == 2) {
				$selected2 ='selected="selected"'; 
			}

			echo ' <select name="dtc_settings['.$this->slug.'_display]"><option value="0" '.$selected0.'>Nobody</option><option value="1" '.$selected1.'>Admins</option><option value="2" '.$selected2.'>Everyone</option></select></td>
		</tr>
		';
	}

	function restrict_pages() {
		global $current_user, $post;
			
		$restrict_pages = array(30349);
		
		if (in_array($post->ID,$restrict_pages ) && !is_admin() && is_user_logged_in()  == false) {
				wp_redirect('/login/?redirect=/import-team/');	
		}
	}

	function ajax_get_league_players() {
		global $wpdb, $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$team_id = $_POST['my_team_id'] ?? '';

		$plugin_base_url = plugin_dir_url(__DIR__);
		echo'
		<table class="mfl-trade-table">
			<thead>
				<tr>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-trade.svg" title="TRADE"></t>
					<th style="min-width:24px; padding-top:2px; text-align:left"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-player.svg" title="PLAYER"></t>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-position.svg" title="POSITION"></th>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-value.svg" title="DTC VALUE"></th>
				</tr>
			</thead>
		<tbody>
		';

		$rules_array = ! empty( $rules_array ) ? $rules_array : [];

		$roster = $this->getTeamRoster($team_id);

		// print_r($roster);

		foreach($roster as $player_o) {
			if ( ! empty( $player_o->position ) && in_array($player_o->position,array('QB','RB','WR','TE'))) {
				$player_info = $this->get_player($player_o);

				$player_info = ! empty( $player_info ) ? $player_info : [];
				$player_info['mfl_id'] = ! empty( $player_info['mfl_id'] ) ? $player_info['mfl_id'] : '';
			
				if ($player_info) {
					#print_r($player_info);	
					echo '	
					<tr>
						<td style="text-align:center"><input type="checkbox" data-id="player" name="trade-' . sanitize_text_field( $_POST['side'] ) . '[]" class="mfl-trade-object" data-side="' . sanitize_text_field( $_POST['side'] ) . '" value="'.$player_info['id'].'"  data-nonppr="'.$player_info['nonppr'].'" data-type="'.$player_info['type'].'"></td>
						<td class="mfl-trade-table-player-team" style="text-align:left; padding-left:5px; padding-right:5px;"><span class="mfl-trade-table-player">'.stripslashes($player_info['name']).'</span> <span class="mfl-trade-table-team">'.$player_info['team'].'</span></td>
						<td style="text-align:center; padding-left:3px; padding-right:3px;">
					';
					
					if ($player_info['position'] == 'TE') {
						echo '<span style="display:none">Z</span>';	
					}

					echo ''.$player_info['position'].'
						</td>
						<td  style="text-align:center">'.dtc_get_player_total($player_info['mfl_id'],$rules_array,true).'</td>
					</tr>
					';
				}
			}
		}

		$season = date("Y");

		$settingsXml = $this->getLeagueSettings($league_id);
		if ($settingsXml !== false) {
			$xmlString = simplexml_load_string($settingsXml);
			$season = (int)$xmlString->league->season;

			if (is_int($season)) {
				$season += 1;
			}

			$trade_draft_picks = (int)$xmlString->league->settings->can_trade_draft_picks;
		} else {
			$trade_draft_picks = 1;
		}

		if ($this->includeDraftPicks && $trade_draft_picks === 1) {
			$draft_picks = $this->getTeamDraftPicks($league_id, $team_id, $this->rounds, $season);

			foreach($draft_picks as $pick) {
				$pick_data = $this->get_pick($pick->year . ' ' . $pick->pickValue);
	
				if (!empty($pick_data)) {
					$pick_data = empty( $pick_data ) ? [] : $pick_data;
				
					$pick_data['nonppr'] = ! empty( $pick_data['nonppr'] ) ? $pick_data['nonppr'] : '';
					
					$pick_data['ten'] =  ! empty( $pick_data['ten'] ) ? $pick_data['ten'] : '';
					$pick_data['twelve'] = ! empty( $pick_data['twelve'] ) ? $pick_data['twelve'] : '';
					$pick_data['fourteen'] = ! empty( $pick_data['fourteen'] ) ? $pick_data['fourteen'] : '';
					$pick_data['sixteen'] = ! empty( $pick_data['sixteen'] ) ? $pick_data['sixteen'] : '';
	
					$pick_data['tensf'] = ! empty( $pick_data['tensf'] ) ? $pick_data['tensf'] : '';
					$pick_data['twelvesf'] = ! empty( $pick_data['twelvesf'] ) ? $pick_data['twelvesf'] : '';
					$pick_data['fourteensf'] = ! empty( $pick_data['fourteensf'] ) ? $pick_data['fourteensf'] : '';
					$pick_data['sixteensf'] = ! empty( $pick_data['sixteensf'] ) ? $pick_data['sixteensf'] : '';
					$pick_data['id'] = ! empty( $pick_data['id'] ) ? $pick_data['id'] : '';
					
					// Have defaulted data-pick to 0 as we don't know what it is.
					echo '	
					<tr>
						<td style="text-align:center"><input type="checkbox" data-id="pick" class="mfl-trade-object" data-side="' . sanitize_text_field( $_POST['side'] ) . '" data-type=""  class="trade-' . sanitize_text_field( $_POST['side'] ) . '" data-year="'.$pick->year.'" data-round="'.$pick->round.'" data-pick="0"   data-nonppr="'.$pick_data['nonppr'].'" data-ten="'.$pick_data['ten'].'" data-twelve="'.$pick_data['twelve'].'" data-fourteen="'.$pick_data['fourteen'].'" data-sixteen="'.$pick_data['sixteen'].'" data-tensf="'.$pick_data['tensf'].'" data-twelvesf="'.$pick_data['twelvesf'].'" data-fourteensf="'.$pick_data['fourteensf'].'" data-sixteensf="'.$pick_data['sixteensf'].'" value="'.$pick_data['id'].'"></td>
						<td  style="text-align:left; padding-left:5px; padding-right:5px;" >'.$pick->displayText.' </td>
						<td style="font-size:0px;">ZZ</td>
						<td style="text-align:center">'.dtc_get_player_total(0,$rules_array,true,'draft_picks',$pick->year . ' ' . $pick->pickValue).'</td>
					</tr>
					';
				} else {
					// Pick not found. Not sure what to do here?
				}
			}
		}
		echo '</tbody></table>';
		die();
	}

	function fix_name($name) {
		$name = str_replace("","%",$name);
		$name = str_replace(" Jr.","%",$name);
		$name = str_replace(" Sr.","%",$name);
		$name = str_replace("'","%",$name);
		$name = str_replace('"','%',$name);
		$name = str_replace(".",'%',$name);
		$name = str_replace("-",'%',$name);

		return $name;
	}

	function get_player($player) {
		global $wpdb;
		
		$query = "SELECT * FROM  wp_dtc_players where name like '%".$this->fix_name($player->player_first_name)."%".$this->fix_name($player->player_last_name)."%' AND position = '".$this->fix_name($player->position)."'";

		$r = $wpdb->get_results($query, ARRAY_A);
		
		if ($r) {
			return $r[0];
		} else {
			return false;	
		}
	}

	function get_pick($pick_id) {
		global $wpdb;
	
		// Try to get the exact pick
		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM wp_dtc_draft_picks WHERE pick = %s", $pick_id), ARRAY_A);
	
		if (!empty($r)) {
			return $r[0]; // Return the exact match
		}
	
		// Extract year and round from the pick_id
		if (preg_match('/^(\d{4}) (\d+)\.\d+$/', $pick_id, $matches)) {
			$year = $matches[1];
			$round = (int) $matches[2];
	
			// Construct the "Mid" value
			$mid_pick = sprintf("%s %s (Mid)", $year, $this->ordinal($round));
	
			// Try to find the mid pick
			$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM wp_dtc_draft_picks WHERE pick = %s", $mid_pick), ARRAY_A);
			
			return $r[0] ?? '';
		}
	
		return '';
	}

	function javascript_functions() {
	?>
		<script type="text/javascript">
			function dtc_refresh_yahoo_loader() {
				add_dtc_mfl_create_overlay(".mfl-loader");
				jQuery(".league-import-item").hide();
		
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_main_loader'},
					function(response) {					
						jQuery(".<?php echo $this->slug; ?>-loader").html(response);
						jQuery(".<?php echo $this->slug; ?>-loader").show();
				});
			}

			function dtc_<?php echo $this->slug; ?>_load_team(league_id,franchise_id,team_name,location,side){
				add_dtc_mfl_create_overlay(location);
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_players','side':side, 'league_id':league_id,'my_team_id':franchise_id,'my_team_name':team_name}, function(response) {					
						jQuery(location).html(response);
					//	jQuery(location).prepend('<h3>' + team_name + '</h3>');
				});
			}

			jQuery(document).ready(function($) {
				$( document ).on( "click", ".<?php echo $this->slug; ?>-logout",
					function() {
						let sbutmitButton = $(this);
						let currentText = sbutmitButton.text();
						sbutmitButton.text('Logging Out...');
						sbutmitButton.attr('disabled', true);
				
						$.post(dtc.ajaxurl, {'action':'dtc_<?php echo $this->slug; ?>_ajax_logout', 'user_id':$(this).attr('data-id')},
							function(response) {
								// Making sure to clear out the values on logout
								localStorage.removeItem('dtc_league_name');
								localStorage.removeItem('dtc_league_id');

								// Ensure we stay on the Yahoo tab after logout
								localStorage.setItem('dtc_integration_name', 'yahoo');

								window.IntegrationLeagueName = undefined;
								window.IntegrationLeagueId = undefined;

								window.rotoGPT_LeagueToken = undefined;
								window.rotoGPT_LeagueId = undefined;
								window.rotoGPT_LeagueName = undefined;
								window.rotoGPT_UserEmail = undefined;

								var event = new Event('IntegrationLeagueNameChanged');
								window.dispatchEvent(event);

								dtc_refresh_main_loader();
								dtc_ga_track('<?php echo $this->name; ?>','User Logout');
								sbutmitButton.text(currentText);
								sbutmitButton.attr('disabled', false);
							});

						return false;		
					}
				);

				$( document ).on( "click", ".<?php echo $this->slug; ?>-refresh-data",
					function() {
						$.post(dtc.ajaxurl, {'action':'dtc_<?php echo $this->slug; ?>_ajax_refresh_data', 'user_id':$(this).attr('data-id')},
							function(response) {					
								dtc_refresh_main_loader();
								alert("Refreshed League Data");
								dtc_ga_track('<?php echo $this->name; ?>','Refresh Data');
							});
				
						return false;
					}
				);

				$( document ).on( "change", ".<?php echo $this->slug; ?>_load_opposing_team", function() {
					dtc_<?php echo $this->slug; ?>_load_team(jQuery(this).attr('data-id'),jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-name'),".<?php echo $this->slug; ?>-import-team-two","right");	
				});

				$( document ).on( "change", ".yahoo_your_team", function() {
					$(".<?php echo $this->slug; ?>-import-team-two").empty();
					$(".<?php echo $this->slug; ?>-import-team-one").empty();	
					$(".<?php echo $this->slug; ?>_load_opposing_team").val("");
			
					$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_settings', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id')}, function(response) {					
						$(".<?php echo $this->slug; ?>-league-settings").html(response);
						dtc_ga_track('<?php echo $this->name; ?>','League Settings');					
					});
			
					dtc_<?php echo $this->slug; ?>_load_team(jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-id'),jQuery('option:selected', this).attr('data-my-team-name'),".<?php echo $this->slug; ?>-import-team-one","left");
				
					$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_opposing_players_dropdown', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id'),'my_team_name':$('option:selected', this).attr('data-my-team-name')}, function(response) {					
						$(".<?php echo $this->slug; ?>-opposing-teams").html(response);
						//
					});
				});
			});
		</script>
	<?php
	}


	/****************************************************************************************
	 * 
	 * 
	 *              YAHOO Data Manipulation Functions
	 *              (Mapping it to what we need for DTC)
	 * 
	 * 
	 ****************************************************************************************/
	public function get_leagues() {
		$leagues = [];

		$leaguesData = $this->getAllLeagueInfo();

		foreach ($leaguesData as $leagueInfo) {

			$teams = $this->getRosterDataForLeague($leagueInfo->league_key);

			foreach ($teams as $team) {
				if ($team->owner->owner_id === $this->userId) {
					$userTeam = $team; // Set user team if owner ID matches
					break; // Exit the loop once the team is found
				}
			}

			$leagueTeam = new stdClass();
			$leagueTeam->leagueId = $leagueInfo->league_key;
			$leagueTeam->leagueName = $leagueInfo->league_name;
			$leagueTeam->teamId = $userTeam->team_id;
			$leagueTeam->teamName = $userTeam->team_name;

			$leagues[] = $leagueTeam;
		}
		
		return $leagues;
	}

	public function get_opponents($league_id, $my_team_id) {
		$teams_list = [];

		$leagueInfo = $this->getSettingsDataForLeague($league_id);

		$teams = $this->getRosterDataForLeague($league_id);

		foreach ($teams as $team) {
			if ($team->owner->owner_id !== $my_team_id) {
				$leagueTeam = new stdClass();
				$leagueTeam->leagueId = $leagueInfo->league_id;
				$leagueTeam->leagueName = $leagueInfo->league_name;
				$leagueTeam->teamId = $team->team_id;
				$leagueTeam->teamName = $team->team_name;
	
				$teams_list[] = $leagueTeam;
			}
		}
		
		return $teams_list;
	}

	public function getAllLeagueInfo() {
		$allLeagueInfo = [];

		// Get leagues XML data
		$leaguesXml = $this->getAllLeaguesForUser();
		if ($leaguesXml === false) {
			return [];
		}

		// Parse the XML using SimpleXML
		$xmlString = simplexml_load_string($leaguesXml);

		// Check if the XML was parsed correctly
		if ($xmlString === false) {
			// Handle the error, e.g. log it
			error_log("Failed to parse XML: " . libxml_get_errors());
			return [];
		}

		// Check if user_id is set, else find the guid
		if ($this->userId === null) {
			$userGuid = (string) $xmlString->users->user->guid; // Correctly navigate to user GUID
			$this->userId = $userGuid;
		}

		// Find all leagues in the XML
		foreach ($xmlString->users->user->games->game->leagues->league as $league) {
			$leagueId = (string) $league->league_id;
			$leagueKey = (string) $league->league_key;
			$leagueName = (string) $league->name;
			$draftStatus = (string) $league->draft_status;
			$numTeams = (int) $league->num_teams;
			$currentWeek = (int) $league->current_week;
			$startWeek = (int) $league->start_week;
			$endWeek = (int) $league->end_week;
			$season = (int) $league->season;
	
			// Create a new YahooLeagueInfo object and add it to the array
			$leagueInfo = new stdClass();
			$leagueInfo->league_id = $leagueId;
			$leagueInfo->league_key = $leagueKey;
			$leagueInfo->league_name = $leagueName;
			$leagueInfo->draft_status = $draftStatus;
			$leagueInfo->num_teams = $numTeams;
			$leagueInfo->current_week = $currentWeek;
			$leagueInfo->start_week = $startWeek;
			$leagueInfo->end_week = $endWeek;
			$leagueInfo->season = $season;

			$allLeagueInfo[] = $leagueInfo;
		}
		
		return $allLeagueInfo;
	}

	function getSettingsDataForLeague($league_id) {
		$settingsXml = $this->getLeagueSettings($league_id);
		if ($settingsXml === false) {
			return [];
		}
	
		$xmlString = simplexml_load_string($settingsXml);

		$draftTimeMs = (int)$xmlString->league->settings->draft_time;
		$draftTimeS = $draftTimeMs / 1000;
		$draftStartTime = date('Y-m-d H:i:s', $draftTimeS);

		$settings = $this->calculateLeagueSettings($xmlString);

		/*
		$settings = [
			"superflex" => true,
			"2QB" => false,
			"standard" => false,
			"TE_Premium" => false,
			"RB_PPC" => true,
		];
		*/

		$leagueFormat = $this->checkPpr($xmlString);

		$leagueStatus = $this->getLeagueStatus($xmlString);

		$positions = [];

		foreach ($xmlString->league->settings->roster_positions->roster_position as $rosterPosition) {
			$position = (string)$rosterPosition->position;
			$count = (int)$rosterPosition->count;

			for ($i = 0; $i < $count; $i++) {
				$positions[] = $position;
			}
		}

		$leagueInfo = new stdClass();
		$leagueInfo->league_id = (string)$xmlString->league->league_key;
		$leagueInfo->league_name = (string)$xmlString->league->name;
		$leagueInfo->league_size = (int)$xmlString->league->num_teams;
		$leagueInfo->league_format = $leagueFormat;
		$leagueInfo->settings = $settings;
		$leagueInfo->draft_date = $draftStartTime;
		$leagueInfo->league_status = $leagueStatus;
		$leagueInfo->roster_positions = $positions;

		return $leagueInfo;
	}

	// We check if it exists in global before retrieving it
	function getRosterDataForLeague($league_id) {
		global $leagueTeams;
	
		if (isset($leagueTeams[$league_id])) {
			return $leagueTeams[$league_id];
		}

		$leagueTeams[$league_id] = $this->getRosters($league_id);

		return $leagueTeams[$league_id];
	}

	function getRosters($league_id) {
		$standingsXml = $this->getLeagueStandings($league_id); // Assuming this function is already defined
		if ($standingsXml === false) {
			return []; // Return an empty array if fetching standings fails
		}
	
		// Load the standings XML into a SimpleXML object
		$xmlString = simplexml_load_string($standingsXml);

		$teams = $xmlString->league->standings->teams->team; // Find all <team> elements
		$leagueTeams = [];
	
		foreach ($teams as $team) {
			$teamId = (string) $team->team_key;
			$teamName = (string) $team->name;
			$draftPosition = (string) $team->draft_position;
	
			// Access team standings
			$teamStandings = $team->team_standings;
			$rank = (string) $teamStandings->rank;
			$wins = (string) $teamStandings->outcome_totals->wins;
			$losses = (string) $teamStandings->outcome_totals->losses;
			$pointsFor = (string) $teamStandings->points_for;
			$pointsAgainst = (string) $teamStandings->points_against;
	
			// Access manager information
			$firstManager = $team->managers->manager;
			$ownerId = $firstManager ? (string) $firstManager->guid : null;
			$ownerName = $firstManager ? (string) $firstManager->nickname : null;
	
			// Get team roster
			$roster = $this->getTeamRoster($teamId);
	
			// Determine current rank
			$currentRank = empty($rank) ? (int) $draftPosition : (int) $rank;
	
			// Create league standings array
			$leagueStandings = [
				"current_rank" => $currentRank,
				"wins" => $wins,
				"losses" => $losses,
				"fantasy_points_for" => $pointsFor,
				"fantasy_points_against" => $pointsAgainst,
			];
	
			// Create owner object
			$owner = new stdClass();
			$owner->owner_id = $ownerId;
			$owner->owner_name = $ownerName;
	
			// Create current team object
			$currentTeam = new stdClass();
			$currentTeam->team_id = $teamId;
			$currentTeam->team_name = $teamName;
			$currentTeam->owner = $owner;
			// $currentTeam->players = $roster;
			// $currentTeam->league_standings = $leagueStandings;
			// $currentTeam->draft_picks = $draftPicks;
	
			$leagueTeams[] = $currentTeam; // Add the current team to the league teams array
		}
	
		return $leagueTeams; // Return the array of teams
	}

	function getTeamRoster($team_key) {
		$rosterXml = $this->getTeamPlayers($team_key); // Assuming this function is defined to get roster XML
		if ($rosterXml === false) {
			return []; // Return an empty array if fetching the roster fails
		}
	
		// Load the roster XML into a SimpleXML object
		$xmlString = simplexml_load_string($rosterXml);
	
		$players = $xmlString->team->roster->players->player;
		// $players = $xmlString->xpath('//player'); // Find all <player> elements
		$roster = [];
	
		foreach ($players as $player) {
			$playerId = (string) $player->player_id;
			$playerFirstName = (string) $player->name->first;
			$playerLastName = (string) $player->name->last;
			$playerTeam = (string) $player->editorial_team_abbr;
			$position = (string) $player->display_position;

			$currentPlayer = new stdClass();
			$currentPlayer->player_id = $playerId;
			$currentPlayer->player_first_name = $playerFirstName;
			$currentPlayer->player_last_name = $playerLastName;
			$currentPlayer->player_team = strtoupper($playerTeam);
			$currentPlayer->position = $position;

			$roster[] = $currentPlayer;
		}

		$positionOrder = [
			'QB' => 1,
			'RB' => 2,
			'WR' => 3,
			'TE' => 4
		];
	
		// Sort the roster by the custom position order
		usort($roster, function($a, $b) use ($positionOrder) {
			$posA = $positionOrder[$a->position] ?? PHP_INT_MAX;
			$posB = $positionOrder[$b->position] ?? PHP_INT_MAX;
			return $posA <=> $posB;
		});
	
		return $roster; // Return the processed roster
	}

	function getDraftPicks($league_key) {
		$transactionsXml = $this->getLeagueTransactions($league_key);

		$tradedPicksTransactions = $this->findTradePicksWithTimestamp($transactionsXml);

		$tradedPicks = $this->getCurrentDraftPickOwners($tradedPicksTransactions);

		return $tradedPicks;
	}

	function findTradePicksWithTimestamp($xmlData) {
		$picksWithTimestamps = [];
		$xml = simplexml_load_string($xmlData);
		
		foreach ($xml->league->transactions->transaction as $trade) {
			if ($trade->type == 'trade') {
				$timestamp = isset($trade->timestamp) ? (int) $trade->timestamp : null;
				if ($timestamp) {
					// Convert timestamp to datetime (assuming it's in seconds)
					$timestamp = gmdate('Y-m-d H:i:s', $timestamp);
				} else {
					$timestamp = null;
				}
		
				foreach ($trade->picks->pick as $pick) {
					$pickData = [
						'timestamp' => $timestamp,
						'source_team_key' => isset($pick->source_team_key) ? (string) $pick->source_team_key : null,
						'source_team_name' => isset($pick->source_team_name) ? (string) $pick->source_team_name : null,
						'destination_team_key' => isset($pick->destination_team_key) ? (string) $pick->destination_team_key : null,
						'destination_team_name' => isset($pick->destination_team_name) ? (string) $pick->destination_team_name : null,
						'original_team_key' => isset($pick->original_team_key) ? (string) $pick->original_team_key : null,
						'original_team_name' => isset($pick->original_team_name) ? (string) $pick->original_team_name : null,
						'round' => isset($pick->round) ? (string) $pick->round : null
					];
					$picksWithTimestamps[] = $pickData;
				}
			}
		}
		
		// Sort picks by timestamp
		usort($picksWithTimestamps, function($a, $b) {
			$timestampA = $a['timestamp'] ? strtotime($a['timestamp']) : PHP_INT_MAX;
			$timestampB = $b['timestamp'] ? strtotime($b['timestamp']) : PHP_INT_MAX;
			return $timestampA - $timestampB;
		});
	
		return $picksWithTimestamps;
	}

	function getCurrentDraftPickOwners($tradeData) {
		$pickOwnership = [];
	
		foreach ($tradeData as $trade) {
			$originalTeamKey = $trade['original_team_key'];
			$originalTeamName = $trade['original_team_name'];
			$destinationTeamKey = $trade['destination_team_key'];
			$destinationTeamName = $trade['destination_team_name'];
			$roundNumber = $trade['round'];
	
			// Use (original team key, round) to uniquely identify each pick
			$pickId = $originalTeamKey . '_' . $roundNumber;
	
			// If it's the first trade involving this pick, set the original team details
			if (!isset($pickOwnership[$pickId])) {
				$pickOwnership[$pickId] = [
					'original_team_key' => $originalTeamKey,
					'original_team_name' => $originalTeamName,
					'round' => $roundNumber,
					'current_team_key' => null,
					'current_team_name' => null,
				];
			}
	
			// Update the current owner details
			$pickOwnership[$pickId]['current_team_key'] = $destinationTeamKey;
			$pickOwnership[$pickId]['current_team_name'] = $destinationTeamName;
		}
	
		// Flatten the pick ownership details into a list for the final output
		$result = [];
		foreach ($pickOwnership as $pickData) {
			$result[] = [
				'original_team_key' => $pickData['original_team_key'],
				'original_team_name' => $pickData['original_team_name'],
				'current_team_key' => $pickData['current_team_key'],
				'current_team_name' => $pickData['current_team_name'],
				'round' => $pickData['round']
			];
		}
	
		return $result;
	}

	function getTeamDraftPicks($leagueKey, $teamKey, $rounds, $year) {
		$draftPicks = [];
	
		$tradedPicks = $this->getDraftPicks($leagueKey);

		for ($roundNum = 1; $roundNum <= $rounds; $roundNum++) {
			// Determine the pick value with the correct ordinal suffix
			if ($roundNum == 1) {
				$pickValue = "1st (Mid)";
			} elseif ($roundNum == 2) {
				$pickValue = "2nd (Mid)";
			} elseif ($roundNum == 3) {
				$pickValue = "3rd (Mid)";
			} else {
				$pickValue = "{$roundNum}th (Mid)";
			}
	
			// Assume the team holds the pick initially
			$includePick = true;
	
			// Check all traded picks in the current round
			foreach ($tradedPicks as $pick) {
				// Only consider picks that match the current round
				if ((int) $pick['round'] === $roundNum) {
					// If the team originally had the pick and traded it away, set includePick to false
					if ($pick['original_team_key'] === $teamKey && $pick['current_team_key'] !== $teamKey) {
						$includePick = false;
					}

					// If the team has received the pick, append it
					if ($pick['current_team_key'] === $teamKey) {
						$draftPick = new stdClass();
						$draftPick->year = $year;
						$draftPick->round = $roundNum;
						$draftPick->pickValue = $pickValue;

						if ($pick['original_team_key'] === $teamKey && $pick['current_team_key'] === $teamKey) {
							$draftPick->displayText = $year . ' Draft Pick Round ' . $roundNum;
						} else {
							$draftPick->displayText = $year . ' Draft Pick Round ' . $roundNum . ' from ' . $pick['original_team_name'];
						}
						
						$draftPicks[] = $draftPick;
					}
				}
			}
	
			// If the team hasn't traded the pick away and should keep it, add the pick to the list
			if ($includePick) {
				$draftPick = new stdClass();
				$draftPick->year = $year;
				$draftPick->round = $roundNum;
				$draftPick->pickValue = $pickValue;
				$draftPick->displayText = $year . ' Draft Pick Round ' . $roundNum;

				$draftPicks[] = $draftPick;
			}
		}
	
		return $draftPicks;
	}

	function calculateLeagueSettings($xmlString) {
		$standard = null;
		$twoQB = null;
	
		// Find the QB roster position
		$qbPosition = null;

		foreach ($xmlString->league->settings->roster_positions->roster_position as $position) {
			if ((string) $position['position'] === 'QB') {
				$qbPosition = $position;
				break;
			}
		}
		// $qbPosition = $xmlString->xpath('//league/settings/roster_positions/roster_position[@position="QB"]');
		// $qbPosition = $xmlString->xpath('//roster_position[@position="QB"]');
	
		if (!empty($qbPosition)) {
			$count = (int) $qbPosition[0]->count; // Get the count of QBs
			if ($count == 1) {
				$standard = true;
			} elseif ($count == 2) {
				$twoQB = true;
			} else {
				$twoQB = false;
			}
		} else {
			$twoQB = false;
		}
	
		$superflex = null;
		// Find the superflex position (Q/W/R/T)
		$superflexPosition = null;

		foreach ($xmlString->league->settings->roster_positions->roster_position as $position) {
			if ((string) $position['position'] === 'Q/W/R/T') {
				$superflexPosition = $position;
				break;
			}
		}

		// $superflexPosition = $xmlString->xpath('//league/settings/roster_positions/roster_position[@position="Q/W/R/T"]');
	
		if (!empty($superflexPosition)) {
			$count = (int) $superflexPosition[0]->count; // Get the count for superflex
			$superflex = $count > 0;
		} else {
			$superflex = false;
		}
	
		$rbPPC = $this->checkRbPpc($xmlString); // Assuming checkRbPpc function is defined elsewhere
	
		// No TE Premium available in Yahoo settings
		$settings = [
			"superflex" => $superflex,
			"2QB" => $twoQB,
			"standard" => $standard,
			"TE_Premium" => false,
			"RB_PPC" => $rbPPC,
		];
	
		return $settings; // Return the calculated settings
	}

	function checkRbPpc($xmlString) {
		//$stat = findStatIdByName($xmlString, 'Rushing Attempts');
		$stat = null;

		if ($stat !== null) {
			$statValue = checkStatModifier($xmlString, $stat);
	
			// PPC League
			if ($statValue !== null && $statValue > 0) {
				return true;
			}
		}
	
		return false;
	}
	
	function checkPpr($xmlString) {
		$stat = $this->findStatIdByName($xmlString, 'Receptions');
	
		if ($stat !== null) {
			$statValue = $this->checkStatModifier($xmlString, $stat);
	
			if ($statValue !== null) {
				if ($statValue == 0.5) {
					return "HALF PPR";
				} elseif ($statValue == 1) { // Change to 1 for standard PPR
					return "PPR";
				}
			}
		}
	
		return "NON PPR";
	}
	
	function getLeagueStatus($xmlString) {
		$draftStatus = (string) $xmlString->league->draft_status;
		$currentWeek = (int) $xmlString->league->current_week;
		$playoffStartWeek = (int) $xmlString->league->settings->playoff_start_week;
	
		if ($draftStatus == 'predraft') {
			return "pre_draft";
		}
	
		if ($currentWeek !== null) {
			if ($currentWeek <= 0 || $currentWeek >= 19) {
				return "complete";
			} else {
				return "in_season";
			}
		}
	
		return "pre_draft";
	}
	
	function findStatIdByName($xmlString, $statName) {
		$stat = null;

		foreach ($xmlString->league->settings->stat_categories->stats->stat as $statElement) {
			if ((string) $statElement['name'] === $statName) {
				$stat = $statElement;
				break;
			}
		}

		// $stat = $xmlString->xpath('//league/settings/stat_categories/stats/stat[@name="' . $statName . '"]');
		if (!empty($stat)) {
			$statId = (string) $stat[0]->stat_id;
			return $statId;
		}
		return null;
	}
	
	function checkStatModifier($xmlString, $statId) {
		$statModifier = null;

		foreach ($xmlString->league->settings->stat_modifiers->stats->stat as $statElement) {
			if ((string) $statElement['stat_id'] === $statId) {
				$statModifier = $statElement;
				break;
			}
		}

		//$statModifier = $xmlString->xpath('//league/settings/stat_modifiers/stats/stat[@stat_id="' . $statId . '"]');
		if (!empty($statModifier)) {
			$value = (string) $statModifier[0]->value;
			return $value;
		}
		return null;
	}


	/****************************************************************************************
	 * 
	 * 
	 *              YAHOO Web API Functions
	 *              (Retrieving the data from Yahoo)
	 * 
	 * 
	 ****************************************************************************************/
	function getAllLeaguesForUser() {
		$token = $this->get_token();

		$url = 'https://fantasysports.yahooapis.com/fantasy/v2/users;use_login=1/games;game_keys=nfl/leagues';
	
		// Initialize a cURL session
		$ch = curl_init($url);
	
		// Set the headers, including the Authorization token
		$headers = [
			'Authorization: Bearer ' . $token
		];
		
		// Set the cURL options
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	
		// Execute the cURL request
		$response = curl_exec($ch);
	
		// Check for errors in the cURL request
		if (curl_errno($ch)) {
			echo 'Request Error:' . curl_error($ch);
			return;
		}
	
		// Get the HTTP status code
		$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	
		// Close the cURL session
		curl_close($ch);
	
		if ($httpcode == 200) {
			return $response; // Return the XML data as a string
		} else {
			echo "Failed to retrieve data: " . $httpcode;
		}
	}

	function getLeagueSettings($league_key) {
		$token = $this->get_token();

		$url = 'https://fantasysports.yahooapis.com/fantasy/v2/league/' . urlencode($league_key) . '/settings';
	
		$ch = curl_init($url);
		$headers = [
			'Authorization: Bearer ' . $token
		];
	
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	
		$response = curl_exec($ch);
		$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);
	
		if ($httpcode == 200) {
			return $response; // Return XML data
		} else {
			// echo "Failed to retrieve league settings: HTTP Status $httpcode";
			return false;
		}
	}
	
	function getLeagueStandings($league_key) {
		$token = $this->get_token();

		$url = 'https://fantasysports.yahooapis.com/fantasy/v2/league/' . urlencode($league_key) . '/standings';

		$ch = curl_init($url);
		$headers = [
			'Authorization: Bearer ' . $token
		];
	
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	
		$response = curl_exec($ch);
		$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);
	
		if ($httpcode == 200) {
			return $response; // Return XML data
		} else {
			echo "Failed to retrieve league standings: HTTP Status $httpcode";
			return false;
		}
	}
	
	function getTeamPlayers($team_key) {
		$token = $this->get_token();

		$url = 'https://fantasysports.yahooapis.com/fantasy/v2/team/' . urlencode($team_key) . '/roster/players';
	
		$ch = curl_init($url);
		$headers = [
			'Authorization: Bearer ' . $token
		];
	
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	
		$response = curl_exec($ch);
		$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);
	
		if ($httpcode == 200) {
			return $response; // Return XML data
		} else {
			// Sometimes this error is because of duplicate requests
			// echo "Failed to retrieve team roster: HTTP Status $httpcode";
			return false;
		}
	}

	function getLeagueTransactions($league_key) {
		$token = $this->get_token();

		$url = 'https://fantasysports.yahooapis.com/fantasy/v2/league/' . urlencode($league_key) . '/transactions';

		$ch = curl_init($url);
		$headers = [
			'Authorization: Bearer ' . $token
		];
	
		curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	
		$response = curl_exec($ch);
		$httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
		curl_close($ch);
	
		if ($httpcode == 200) {
			return $response; // Return XML data
		} else {
			echo "Failed to retrieve league transactions: HTTP Status $httpcode";
			return false;
		}
	}
	
}