<?php 

new  ffpc_api;

class ffpc_api {
	protected $slug;
	protected $name;
	// protected $username;
	protected $endpoint;
	protected $season;
	protected $api_url;
    
	protected $userId;
	protected $leagueTeams;
	protected $rounds;
	protected $includeDraftPicks;
	
	public function __construct(){
		$this->slug = 'ffpc';
		$this->name = 'FFPC';
		// $this->username = 'FFPC';
		$settings = get_option('dtc_settings');
		$this->endpoint = isset($settings[$this->slug.'_url']) ? $settings[$this->slug.'_url'] : '';
		$this->season = isset($settings[$this->slug.'_season']) ? $settings[$this->slug.'_season'] : '';

        $this->api_url = "https://myffpc.com/";

		// Not sure if this is needed. Leaving it for now.
		$this->rounds = 4;

		$this->includeDraftPicks = true;

		$this->leagueTeams = [];

		add_action('dtc/admin/settings',array($this,'settings'));
		add_action('dtc/admin/settings/api_settings',array($this,'api_settings'));
		add_action('dtc_calculator_bottom', array($this,'view'));
		add_action('dtc/league_import/content',array($this,'import_content'));
		add_action('dtc/league_import/button',array($this,'import_button'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_league_settings', array($this,'ajax_get_league_settings'));	

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_opposing_players_dropdown', array($this,'ajax_get_opposing_players_dropdown'));	
		add_action( 'wp_ajax_nopriv_dtc_'.$this->slug.'_ajax_get_opposing_players_dropdown', array($this,'ajax_get_opposing_players_dropdown'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_get_league_players', array($this,'ajax_get_league_players'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_get_league_players', array($this,'ajax_get_league_players'));
		
		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_login', array($this,'ajax_login'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_login', array($this,'ajax_login'));
		
		add_action( 'wp_ajax_dtc_'.$this->slug.'_ajax_logout', array($this,'ajax_logout'));	
		add_action( 'wp_ajax_nopriv_dtc_'.$this->slug.'_ajax_logout', array($this,'ajax_logout'));

		add_action( 'wp_ajax_dtc_ajax_'.$this->slug.'_main_loader', array($this,'ajax_main_loader'));	
		add_action( 'wp_ajax_nopriv_dtc_ajax_'.$this->slug.'_main_loader', array($this,'ajax_main_loader'));

		add_action('wp_ajax_ffpc_login', array($this,'ffpcLogin'));
		add_action('wp_ajax_nopriv_ffpc_login', array($this,'ffpcLogin'));

		add_action('wp_ajax_process_ffpc_login', array($this,'process_ffpc_login'));
		add_action('wp_ajax_nopriv_process_ffpc_login', array($this,'process_ffpc_login'));
	}

	/* FFPC Basic Integration Functions */

	function ajax_main_loader() {
		global $current_user;
		$user_id = $current_user->ID;

		$user_email = get_user_meta($user_id, 'ffpc_user_email', true);
        $user_name = get_user_meta($user_id, 'ffpc_user_name', true);
        
		if (empty($user_email) && empty($user_name)) {
			$plugin_base_url = plugin_dir_url(__DIR__);
			?>
			<div class="dtc-mfl-nav ffpc-login">
				<div>
					<div style="text-align: center;"><img style="max-width: 40%;" src="<?php echo $plugin_base_url; ?>/asetts/images/api/ffpc-logo.png"></div>
					<h1 id="ffpc-login-text">Login</h1>
				</div>
					<div style="display: flex; justify-content: center; align-items: center; width: 100%;">
						<form id="auth-form" style="max-width: 400px; width: 100%; text-align: center;">
                            <label class="ffpc-label" for="ffpc_identification_type">FFPC Identification Type</label>
                            <select id="ffpc_identification_type" name="ffpc_identification_type">
                                <option value="username">Username</option>
                                <option value="email">Email</option>
                            </select>
                            <label class="ffpc-label" for="ffpc_username">FFPC Username</label>
                            <input type="text" id="ffpc_username" name="ffpc_username" placeholder="FFPC Username" required />
							<label class="ffpc-label" for="ffpc_user_email">FFPC Email</label>
							<input type="text" id="ffpc_user_email" name="ffpc_user_email" placeholder="FFPC Email" required />
							<button id="ffpc_submit_button" type="button">Submit</button>
							<div id="ffpc-login-overlay" style="display: none;"><div class="dtc-mfl-overlay-wrap"><img src="<?php echo DTC_URL; ?>/asetts/images/loading.gif"></div></div>
						</form>
					</div>
				</div>
				<div id="ffpc-loading-area"  style="display:block; text-align: center; color:#000000;">
					Loading . . .
					<br/>Please wait
				</div>
				<script type="text/javascript">
					jQuery(document).ready(function($) {
                        const $identificationType = $("#ffpc_identification_type");
                        const $usernameField = $("#ffpc_username");
                        const $usernameLabel = $('label[for="ffpc_username"]');
                        const $emailField = $("#ffpc_user_email");
                        const $emailLabel = $('label[for="ffpc_user_email"]');

						$('#ffpc_submit_button').on('click.authForm', function(event) {
							event.preventDefault(); // Prevent the default form submission

							document.getElementById('ffpc_submit_button').style.display = 'none';
							document.getElementById('ffpc-login-overlay').style.display = 'block';

							var ffpc_user_email = $('#ffpc_user_email').val();
                            var ffpc_username = $('#ffpc_username').val();

                            var data = '';

                            if ($identificationType.val() === "username") {
                                data = {
                                    action: 'process_ffpc_login',
                                    user_name: ffpc_username
                                };
                            } else if ($identificationType.val() === "email") {
                                data = {
                                    action: 'process_ffpc_login',
                                    user_email: ffpc_user_email
                                };
                            }

							// Send the AJAX request
							$.post('<?php echo admin_url('admin-ajax.php?action=process_ffpc_login'); ?>', data, function(response) {
								if (response.success === true) {
									localStorage.setItem('dtc_integration_name', 'ffpc');
									window.IntegrationName = "ffpc";
									window.rotoGPT_LeagueIntegrationName = "ffpc";
									window.rotoGPT_LeagueUserId = undefined;
                                    window.rotoGPT_LeagueToken = undefined;
									window.rotoGPT_LeagueId = undefined;
									window.rotoGPT_LeagueName = undefined;
									window.rotoGPT_UserEmail = undefined;
									
									dtc_refresh_main_loader();
								} else {
									document.getElementById('ffpc_submit_button').style.display = 'block';
									document.getElementById('ffpc-login-overlay').style.display = 'none';

									// Need to think about what to do here!
									alert(response.data);
								}
							});
						});

                        // Function to toggle visibility
                        function toggleFields() {
                            if ($identificationType.val() === "username") {
                                $usernameField.show();
                                $usernameLabel.show();
                                $emailField.hide();
                                $emailLabel.hide();
                            } else if ($identificationType.val() === "email") {
                                $usernameField.hide();
                                $usernameLabel.hide();
                                $emailField.show();
                                $emailLabel.show();
                            }
                        }

                        // Attach change event listener
                        $identificationType.on("change", toggleFields);

                        // Initialize the correct state on page load
                        toggleFields();
					});


				</script>
			</div>
			<?php

		} else {
			echo '	
			<h1>'.$this->name.'</h1>
			<div class="dtc-mfl-nav">
				<a href="#" class="'.$this->slug.'-refresh-data mfl-button" data-id="'.$current_user->ID.'">Refresh Data</a>
				<a href="#" class="'.$this->slug.'-logout mfl-button" data-id="'.$current_user->ID.'">Logout</a>
			</div>
			';
	
			$leagues = $this->get_leagues();

			if (is_null($leagues)) {
    			// No leagues found
				echo '<div>No Leagues Found</div>';
			} else {
				echo '<div class="dtc-mfl-half">';
				echo '	<div class="dtc-half-inner">';

				$name = 'ffpc_your_team';
				$html = '<select name="'.$name.'" class="'.$name.' league_dropdown"><option value="">Your Team</option>';
			
				foreach ($leagues as $league) {
					$html .= '<option value="'.$league->leagueId.'" data-my-team-id="'.$league->teamId.'" data-my-team-name="'.$league->teamName.'">'.$league->leagueName.' -  '.$league->teamName.'</option>';
				}

				$html .='</select>';

				$html .= '
					<script>
						jQuery(document).ready(function(){
							var league_id = localStorage.getItem("dtc_league_id");
							
							if (league_id) {
								jQuery("select[name=\'ffpc_your_team\'] option[value=\'" + league_id + "\']").prop("selected", true);
								jQuery("select[name=\'ffpc_your_team\']").trigger("change");
							}
						});
					</script>
					';

				echo $html;

				echo '
					</div>
				</div>
				';

				echo '
					<div class="dtc-mfl-half">
						<div class="dtc-half-inner">
				';
				echo '<div class="'.$this->slug.'-opposing-teams"><select disabled="disabled" ><option value="">Your Competitor</option></select></div>';
				echo '
					</div>
				</div>
				
				<div style="clear:both"></div>
				
				<div class="mfl-import-calc">
					<a href="#" class="mfl-import-calc-button">Import to Calc</a>
				</div>
				
				<div class="'.$this->slug.'-league-settings"></div>
				
				<div class="mfl-team-gutter">
					<div class="dtc-mfl-half"><div class="dtc-half-inner">
					<div class="'.$this->slug.'-import-team-one"></div>
				
					</div>
				</div>
				
				<div class="dtc-mfl-half">
					<div class="dtc-half-inner">
						<div class="'.$this->slug.'-import-team-two">
				
						</div>
					</div>
				</div>
				
				<div style="clear:both"></div>
				';
			}
			
		}

		die();
	}

	function ajax_login() {
		$this->request_token();	
		die();
	}

	function ajax_logout() {
		$user_id = get_current_user_id();

		delete_user_meta($user_id, 'ffpc_user_email');
        delete_user_meta($user_id, 'ffpc_user_name');

		echo $this->ajax_main_loader();
	}

	function request_token() {

		global $current_user;
		
		echo $this->ajax_main_loader();
	}

	function is_active() {

		$settings = get_option('dtc_settings');

		if (!isset($settings[$this->slug.'_display']) || $settings[$this->slug.'_display'] == 0) {
			return false;	
		}

		if ($settings[$this->slug.'_display'] == 1 && !current_user_can('manage_options') ) {
			return false;	
		}
			
		return true;
	}

    function process_ffpc_login() {
        global $current_user;
		$user_id = $current_user->ID;

		$loginType = '';
        if (isset($_POST['user_email'])) {
			$user_name = sanitize_text_field($_POST['user_email']); // Sanitize the input

			update_user_meta($user_id, 'ffpc_user_email', $user_name);
            delete_user_meta($user_id, 'ffpc_user_name');

			$loginType = 'Email';

		} else if (isset($_POST['user_name'])) {
			$user_name = sanitize_text_field($_POST['user_name']); // Sanitize the input
			
			update_user_meta($user_id, 'ffpc_user_name', $user_name);
            delete_user_meta($user_id, 'ffpc_user_email');
			$loginType = 'Username';
		} else {
			wp_send_json_error('No username provided.');
		}

		$leagues = $this->get_leagues();

		if (is_null($leagues)) {
			// No leagues found
			wp_send_json_error('Could not find login. Please try again.');
		} else {
			wp_send_json_success($loginType . ' stored successfully!');
		}
	
		wp_die();
    }

	function import_content() {
		if ($this->is_active() == true) {

			$this->javascript_functions();
			?>

			<style type="text/css">
				.<?php echo $this->slug; ?>-loader select{width:100%;}
				.<?php echo $this->slug; ?>-loader {color:#c3c6ca;}
				.<?php echo $this->slug; ?>-loader h1{color:#c3c6ca;}
				.<?php echo $this->slug; ?>-login{min-height:500px;padding:20px 0px;}
				.<?php echo $this->slug; ?>-login input[type=text]{width:80%;border-radius:5px;}
				.<?php echo $this->slug; ?>-login  .dtc-button{-moz-border-radius: 5px;
					border-radius: 26px;
					height: 52px;
					font-size: 14px;
					color: #FFF;
					background-color: #333;
					text-align:center;
					font-weight:600;
					min-width:100px;
				}
			</style>
			<div class="<?php echo $this->slug; ?>-loader league-import-item">
				
			</div>
			<?php
		}
	}

	function import_button() {
		if ($this->is_active()  == true) {
				echo '<a href="" class="dtc-refresh-main-loader '.$this->slug.'-loader-button" data-id="'.$this->slug.'">'.$this->name.'</a>';
		}
	}

	public function view() {	
	}

	function api_settings($settings) {
		$url = isset($settings[$this->slug.'_url']) ? $settings[$this->slug.'_url'] : '';
		$season = isset($settings[$this->slug.'_season']) ? $settings[$this->slug.'_season'] : '';

		echo ' 
		<tr>
			<td style="width:150px !important">'.$this->name.' URL</td>
			<td><input type="text" name="dtc_settings['.$this->slug.'_url]" value="'.$url.'" style="width:300px;max-width:none"> </td>
		</tr>
		<tr>
			<td style="width:150px !important">'.$this->name.' Season</td>
			<td><input type="text" name="dtc_settings['.$this->slug.'_season]" value="'.$season.'" style="width:300px;max-width:none"> </td>
		</tr>
		';
	}

	function ajax_get_league_settings() {
		global $current_user;
		$user_id = $current_user->ID;

		$league_id = $_POST['league_id'] ?? '';

		$leagueInfo = $this->get_settings_for_league($league_id);

        $user_email = get_user_meta($user_id, 'ffpc_user_email', true);
        $user_name = get_user_meta($user_id, 'ffpc_user_name', true);

		?>
			<script type="text/javascript">
				localStorage.setItem('dtc_integration_name', 'ffpc');
				localStorage.setItem('dtc_league_name', '<?php echo $leagueInfo->league_name; ?>');
				localStorage.setItem('dtc_league_id', '<?php echo $league_id; ?>');

				// Populating this so we don't have conflicting uses of rotoGPT and DTC
				window.IntegrationName = "ffpc";
				window.IntegrationLeagueName = "<?php echo $leagueInfo->league_name; ?>";
				window.IntegrationLeagueId = "<?php echo $league_id; ?>";

				window.rotoGPT_LeagueIntegrationName = "ffpc";

				<?php
                if (!empty($user_email)) {
				?>
                    window.rotoGPT_LeagueUserId = "<?php echo $user_email; ?>";
				<?php
                } elseif (!empty($user_name)) {
				?>
                    window.rotoGPT_LeagueToken = "<?php echo $user_name; ?>";
				<?php
                }
				?>

				window.rotoGPT_LeagueId = "<?php echo $league_id; ?>";
				window.rotoGPT_LeagueName = "<?php echo $leagueInfo->league_name; ?>";

				var event = new Event('IntegrationLeagueNameChanged');
				window.dispatchEvent(event);
			</script>
		<?php

		
		$rules_array['team_type'] = $leagueInfo->league_format;
		
		if ($leagueInfo->settings['superflex']) {
			$rules_array['team_format'] = 'SF';
		} else if ($leagueInfo->settings['2QB']) {
			$rules_array['team_format'] = '2QB';
		} else {
			$rules_array['team_format'] = 'STANDARD';
		}

		$rules_array['team_size'] = $leagueInfo->league_size;

		if ($leagueInfo->settings['TE_Premium']) {
			$rules_array['tepre'] = 'YES';
		} else {
			$rules_array['tepre'] = 'NO';
		}

		if ($leagueInfo->settings['RB_PPC']) {
			$rules_array['rbppc'] = 'YES';
		} else {
			$rules_array['rbppc'] = 'NO';
		}

		echo '	
		<input type="hidden" class="mfl-rule-team-type" value="'.$rules_array['team_type'].'">
		<input type="hidden" class="mfl-rule-team-format" value="'.$rules_array['team_format'].'">
		<input type="hidden" class="mfl-rule-team-size" value="'.$rules_array['team_size'].'">
		<input type="hidden" class="mfl-rule-team-tepre" value="'.$rules_array['tepre'].'">
		<input type="hidden" class="mfl-rule-team-rbppc" value="'.$rules_array['rbppc'].'">';

		echo '
		<div class="mfl-league_settings">
			<span><strong>Type: </strong>'.$rules_array['team_type'].'</span>
			<span><strong>Format: </strong>'.$rules_array['team_format'].'</span>
			<span><strong>Size: </strong>'.$rules_array['team_size'].'tm</span>
			<span><strong>TE Premium? </strong>'.$rules_array['tepre'].'</span>
			<span><strong>RB PPC? </strong>'.$rules_array['rbppc'].'</span>
		</div>';

		?>
			<script type="text/javascript">
				_dtc_save_setting('ls', "<?php echo $rules_array['team_size']; ?>");

				if ("<?php echo $rules_array['team_type']; ?>" == "PPR") {
					_dtc_save_setting('lt', "ppr");
				} else if ("<?php echo $rules_array['team_type']; ?>" == "HALF PPR") {
					_dtc_save_setting('lt', "half_ppr");
				} else if ("<?php echo $rules_array['team_type']; ?>" == "NON PPR") {
					_dtc_save_setting('lt', "nonppr");
				}
				
				_dtc_save_setting('lf', "<?php echo strtolower($rules_array['team_format']); ?>");

				var lfa_settings = '';

				if ("<?php echo $rules_array['tepre']; ?>" == "YES") {
					lfa_settings += 'te-premium,';
				}

				if ("<?php echo $rules_array['rbppc']; ?>" == "YES") {
					lfa_settings += 'rb-ppc-premium,';
				}

				lfa_settings += 'offense,';

				_dtc_save_setting('lfa',lfa_settings);

				recalculatePlayers();
			</script>
		<?php

		die();
	}

	function ajax_get_opposing_players_dropdown() {
		global $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$team_id = $_POST['my_team_id'] ?? '';
		
		$class_name = $this->slug . '_load_opposing_team';

		$teams = $this->get_opponents($league_id,$team_id);	
		
		if (!empty($teams)) {
			$firstTeam = $teams[0];
		
			$leagueId = $firstTeam->leagueId;
			$leagueName = $firstTeam->leagueName;

			$league_name = ! empty( $leagueName ) ? $leagueName : '';
			$html = '<select name="'.$class_name.'" class="'.$class_name.'" data-id="'.$leagueId.'" data-name="'.$league_name.'"><option value="">Your Competitor</option>';
			
			foreach($teams as $team) {
				if ($team->teamId !== $team_id) {
					$html .= '<option value="'.$team->teamId.'" data-my-team-name="'.$team->teamName.'" >'.$team->teamName.'</option>';				
				}
			}
			
			$html .='</select>';
			
			echo $html;
		}

		die();
	}

	function settings($settings) {
		$selected0 = '';
		$selected1 = '';
		$selected2 = '';

		$display_setting = isset($settings[$this->slug.'_display']) ? $settings[$this->slug.'_display'] : 0;
		
		echo '  
		<tr>
			<td style="width:150px">'.$this->name.' Display</td>
			<td>';
			if ($display_setting == 0) {
				$selected0 ='selected="selected"'; 
			}

			if ($display_setting == 1) {
				$selected1 ='selected="selected"'; 
			}

			if ($display_setting == 2) {
				$selected2 ='selected="selected"'; 
			}

			echo ' <select name="dtc_settings['.$this->slug.'_display]"><option value="0" '.$selected0.'>Nobody</option><option value="1" '.$selected1.'>Admins</option><option value="2" '.$selected2.'>Everyone</option></select></td>
		</tr>
		';
	}

	function restrict_pages() {
		global $current_user, $post;
			
		$restrict_pages = array(30349);
		
		if (in_array($post->ID,$restrict_pages ) && !is_admin() && is_user_logged_in()  == false) {
				wp_redirect('/login/?redirect=/import-team/');	
		}
	}

	function ajax_get_league_players() {
		global $wpdb, $current_user;
		
		$league_id = $_POST['league_id'] ?? '';
		$team_id = $_POST['my_team_id'] ?? '';

        if (empty($league_id) || empty($team_id)) {
            die();
        }

		$plugin_base_url = plugin_dir_url(__DIR__);
		echo'
		<table class="mfl-trade-table">
			<thead>
				<tr>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-trade.svg" title="TRADE"></t>
					<th style="min-width:24px; padding-top:2px; text-align:left"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-player.svg" title="PLAYER"></t>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-position.svg" title="POSITION"></th>
					<th style="min-width:24px; padding-top:2px;"><img style="width:24px" src="' . $plugin_base_url . '/asetts/images/league-import-value.svg" title="DTC VALUE"></th>
				</tr>
			</thead>
		<tbody>
		';

		$rules_array = ! empty( $rules_array ) ? $rules_array : [];

		$roster = $this->get_team_roster($league_id, $team_id);

		foreach($roster as $player_o) {
			if ( ! empty( $player_o->position ) && in_array($player_o->position,array('QB','RB','WR','TE'))) {
				$player_info = $this->get_player($player_o);

				$player_info = ! empty( $player_info ) ? $player_info : [];
				$player_info['mfl_id'] = ! empty( $player_info['mfl_id'] ) ? $player_info['mfl_id'] : '';
			
				if ($player_info) {
					#print_r($player_info);	
					echo '	
					<tr>
						<td style="text-align:center"><input type="checkbox" data-id="player" name="trade-' . sanitize_text_field( $_POST['side'] ) . '[]" class="mfl-trade-object" data-side="' . sanitize_text_field( $_POST['side'] ) . '" value="'.$player_info['id'].'"  data-nonppr="'.$player_info['nonppr'].'" data-type="'.$player_info['type'].'"></td>
						<td class="mfl-trade-table-player-team" style="text-align:left; padding-left:5px; padding-right:5px;"><span class="mfl-trade-table-player">'.stripslashes($player_info['name']).'</span> <span class="mfl-trade-table-team">'.$player_info['team'].'</span></td>
						<td style="text-align:center; padding-left:3px; padding-right:3px;">
					';
					
					if ($player_info['position'] == 'TE') {
						echo '<span style="display:none">Z</span>';	
					}

					echo ''.$player_info['position'].'
						</td>
						<td  style="text-align:center">'.dtc_get_player_total($player_info['mfl_id'],$rules_array,true).'</td>
					</tr>
					';
				}
			}
		}

		$season = date("Y");

		if ($this->includeDraftPicks) {
			$draft_picks = $this->get_draft_picks($league_id, $team_id);
		
			foreach ($draft_picks as $pick) {
				$pick_data = $this->get_pick($pick->year . ' ' . $pick->pickValue);

				if (!empty($pick_data)) {
					$fields = ['nonppr', 'ten', 'twelve', 'fourteen', 'sixteen', 'tensf', 'twelvesf', 'fourteensf', 'sixteensf', 'id'];
					foreach ($fields as $field) {
						$pick_data[$field] = !empty($pick_data[$field]) ? $pick_data[$field] : '';
					}
		
					echo sprintf('
					<tr>
						<td style="text-align:center">
							<input type="checkbox" data-id="pick" class="mfl-trade-object" data-side="%s" data-type="" class="trade-%s" data-year="%s" data-round="%s" data-pick="0" data-nonppr="%s" data-ten="%s" data-twelve="%s" data-fourteen="%s" data-sixteen="%s" data-tensf="%s" data-twelvesf="%s" data-fourteensf="%s" data-sixteensf="%s" value="%s">
						</td>
						<td style="text-align:left; padding-left:5px; padding-right:5px;">%s</td>
						<td style="font-size:0px;">ZZ</td>
						<td style="text-align:center">%s</td>
					</tr>',
						htmlspecialchars($_POST['side'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($_POST['side'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick->year, ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick->round, ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['nonppr'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['ten'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['twelve'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['fourteen'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['sixteen'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['tensf'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['twelvesf'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['fourteensf'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['sixteensf'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick_data['id'], ENT_QUOTES, 'UTF-8'),
						htmlspecialchars($pick->displayText, ENT_QUOTES, 'UTF-8'),
						htmlspecialchars(dtc_get_player_total(0, $rules_array, true, 'draft_picks', $pick->year . ' ' . $pick->pickValue), ENT_QUOTES, 'UTF-8')
					);
				} else {
					// Ignore this for now.
					// echo '<tr><td colspan="4" style="text-align:center;">Pick data not found</td></tr>';
				}
			}
		}
		
		echo '</tbody></table>';
		die();
	}

	function fix_name($name) {
		$name = str_replace("","%",$name);
		$name = str_replace(" Jr.","%",$name);
		$name = str_replace(" Sr.","%",$name);
		$name = str_replace("'","%",$name);
		$name = str_replace('"','%',$name);
		$name = str_replace(".",'%',$name);
		$name = str_replace("-",'%',$name);

		return $name;
	}

	function get_player($player) {
		global $wpdb;
		
		$query = "SELECT * FROM  wp_dtc_players where name like '%".$this->fix_name($player->player_first_name)."%".$this->fix_name($player->player_last_name)."%' AND position = '".$this->fix_name($player->position)."'";

		$r = $wpdb->get_results($query, ARRAY_A);
		
		if ($r) {
			return $r[0];
		} else {
			return false;	
		}
	}

	function get_pick($pick_id) {
		global $wpdb;
		$r = $wpdb->get_results($wpdb->prepare("SELECT * FROM  wp_dtc_draft_picks where pick = %s", $pick_id), ARRAY_A);
		return $r[0] ?? '';
	}

	function javascript_functions() {
	?>
		<script type="text/javascript">
			function dtc_refresh_ffpc_loader() {
				add_dtc_mfl_create_overlay(".mfl-loader");
				jQuery(".league-import-item").hide();
		
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_main_loader'},
					function(response) {					
						jQuery(".<?php echo $this->slug; ?>-loader").html(response);
						jQuery(".<?php echo $this->slug; ?>-loader").show();
				});
			}

			function dtc_<?php echo $this->slug; ?>_load_team(league_id,franchise_id,team_name,location,side){
				add_dtc_mfl_create_overlay(location);
				jQuery.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_players','side':side, 'league_id':league_id,'my_team_id':franchise_id,'my_team_name':team_name}, function(response) {					
						jQuery(location).html(response);
				});
			}

			jQuery(document).ready(function($) {
				$( document ).on( "click", ".<?php echo $this->slug; ?>-logout",
					function() {
						let sbutmitButton = $(this);
						let currentText = sbutmitButton.text();
						sbutmitButton.text('Logging Out...');
						sbutmitButton.attr('disabled', true);
					
						$.post(dtc.ajaxurl, {'action':'dtc_<?php echo $this->slug; ?>_ajax_logout', 'user_id':$(this).attr('data-id')},
							function(response) {
								// Making sure to clear out the values on logout
								localStorage.removeItem('dtc_league_name');
								localStorage.removeItem('dtc_league_id');

								// Ensure we stay on the FFPC tab after logout
								localStorage.setItem('dtc_integration_name', 'ffpc');

								window.IntegrationLeagueName = undefined;
								window.IntegrationLeagueId = undefined;

								window.rotoGPT_LeagueToken = undefined;
								window.rotoGPT_LeagueId = undefined;
								window.rotoGPT_LeagueName = undefined;
								window.rotoGPT_UserEmail = undefined;

								var event = new Event('IntegrationLeagueNameChanged');
								window.dispatchEvent(event);

								dtc_refresh_main_loader();
								dtc_ga_track('<?php echo $this->name; ?>','User Logout');
								sbutmitButton.text(currentText);
								sbutmitButton.attr('disabled', false);
							});

						return false;		
					}
				);

				$( document ).on( "click", ".<?php echo $this->slug; ?>-refresh-data",
					function() {
						$.post(dtc.ajaxurl, {'action':'dtc_<?php echo $this->slug; ?>_ajax_refresh_data', 'user_id':$(this).attr('data-id')},
							function(response) {					
								dtc_refresh_main_loader();
								alert("Refreshed League Data");
								dtc_ga_track('<?php echo $this->name; ?>','Refresh Data');
							});
				
						return false;
					}
				);

				$( document ).on( "change", ".<?php echo $this->slug; ?>_load_opposing_team", function() {
					dtc_<?php echo $this->slug; ?>_load_team(jQuery(this).attr('data-id'),jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-name'),".<?php echo $this->slug; ?>-import-team-two","right");	
				});

				$( document ).on( "change", ".ffpc_your_team", function() {
					$(".<?php echo $this->slug; ?>-import-team-two").empty();
					$(".<?php echo $this->slug; ?>-import-team-one").empty();	
					$(".<?php echo $this->slug; ?>_load_opposing_team").val("");
			
					$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_league_settings', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id')}, function(response) {					
						$(".<?php echo $this->slug; ?>-league-settings").html(response);
						dtc_ga_track('<?php echo $this->name; ?>','League Settings');					
					});
			
					dtc_<?php echo $this->slug; ?>_load_team(jQuery(this).val(),jQuery('option:selected', this).attr('data-my-team-id'),jQuery('option:selected', this).attr('data-my-team-name'),".<?php echo $this->slug; ?>-import-team-one","left");
				
					$.post(dtc.ajaxurl, {'action':'dtc_ajax_<?php echo $this->slug; ?>_get_opposing_players_dropdown', 'league_id':$(this).val(),'my_team_id':$('option:selected', this).attr('data-my-team-id'),'my_team_name':$('option:selected', this).attr('data-my-team-name')}, function(response) {					
						$(".<?php echo $this->slug; ?>-opposing-teams").html(response);
					});
				});
			});
		</script>
	<?php
	}


	/****************************************************************************************
	 * 
	 * 
	 *              FFPC Data Manipulation Functions
	 *              (Mapping it to what we need for DTC)
	 * 
	 * 
	 ****************************************************************************************/
    public function get_leagues()
    {
        global $current_user;
		$user_id = $current_user->ID;

        $leagues = [];

        $user_email = get_user_meta($user_id, 'ffpc_user_email', true);
        $user_name = get_user_meta($user_id, 'ffpc_user_name', true);

        if (!empty($user_email)) {
            $leaguesXml = $this->getUserLeagues(null, $user_email, null);
        } elseif (!empty($user_name)) {
            $leaguesXml = $this->getUserLeagues($user_name, null, null);
        }
        
        $leagues_data = simplexml_load_string($leaguesXml);

		if ($leagues_data === false || !isset($leagues_data->league)) {
			return null;
		}

        foreach ($leagues_data->league as $league) {
            $leagueId = (string)$league->leagueID;
            $leagueName = (string)$league->leagueName;
            $teamId = (string)$league->teamID;
            $teamName = (string)$league->teamName;
            $ffpcLeagueTypeId = (string)$league->ffpcLeagueTypeID;
            $ffpcLeagueType = (string)$league->ffpcLeagueType;

            $leagueTeam = new stdClass();
			$leagueTeam->leagueId = $leagueId;
			$leagueTeam->leagueName = $leagueName;
			$leagueTeam->teamId = $teamId;
			$leagueTeam->teamName = $teamName;
            $leagueTeam->ffpcLeagueTypeId = $ffpcLeagueTypeId;
            $leagueTeam->ffpcLeagueType = $ffpcLeagueType;

			$leagues[] = $leagueTeam;
        }

        return $leagues;
    }

    public function get_settings_for_league($league_id) {
        $leagueRulesXml = $this->getLeagueRules($league_id, $this->season);

        $leagueInfoData = simplexml_load_string($leagueRulesXml);
        
        $leagueID = (string) $leagueInfoData->league->leagueID;
        $leagueName = (string) $leagueInfoData->league->leagueName;

        $league_size = 0;
        if (isset($leagueInfoData->league->teams->team)) {
            $league_size = count($leagueInfoData->league->teams->team);
        }

        $leagueFormat = $this->checkIfLeagueIsPPR($leagueRulesXml);
        $settings = $this->getLeagueSettings($leagueRulesXml);
        
        // Don't have a draft start time
        $draft_start_time = null;

        $leagueStatus = $this->getLeagueStatus($league_id);

        $positions = $this->getRosterPositions($leagueRulesXml);

        $leagueInfo = new stdClass();
		$leagueInfo->league_id = $leagueID;
		$leagueInfo->league_name = $leagueName;
		$leagueInfo->league_size = (int)$league_size;
		$leagueInfo->league_format = $leagueFormat;
		$leagueInfo->settings = $settings;
		$leagueInfo->draft_date = $draft_start_time;
		$leagueInfo->league_status = $leagueStatus;
		$leagueInfo->roster_positions = $positions;

		return $leagueInfo;
    }

    public function get_opponents($league_id, $my_team_id) {
        $teams_list = [];
    
        // Fetch league rules XML
        $leagueRulesXml = $this->getLeagueRules($league_id, $this->season);
        if ($leagueRulesXml === false) {
            return []; // Return an empty array if fetching the league rules fails
        }
    
        // Parse the XML data
        $leagueInfoData = simplexml_load_string($leagueRulesXml);
        if (!isset($leagueInfoData->league)) {
            return []; // Return an empty array if the XML structure is invalid
        }
    
        // Extract league information
        $leagueId = (string) $leagueInfoData->league->leagueID;
        $leagueName = (string) $leagueInfoData->league->leagueName;
    
        // Loop through the teams
        if (isset($leagueInfoData->league->teams->team)) {
            foreach ($leagueInfoData->league->teams->team as $team) {
                $teamId = (string) $team->teamID;
    
                // Exclude the user's team
                if ($teamId !== (string) $my_team_id) {
                    $leagueTeam = new stdClass();
                    $leagueTeam->leagueId = $leagueId;
                    $leagueTeam->leagueName = $leagueName;
                    $leagueTeam->teamId = $teamId;
                    $leagueTeam->teamName = (string) $team->teamName;
    
                    $teams_list[] = $leagueTeam;
                }
            }
        }
    
        return $teams_list;
    }
    

    function get_team_roster($league_id, $team_key) {
		$rosterXml = $this->getLeagueRosters($league_id);
		if ($rosterXml === false) {
			return []; // Return an empty array if fetching the roster fails
		}
	
		// Load the roster XML into a SimpleXML object
		$xmlString = simplexml_load_string($rosterXml);
	
		$roster = [];

        foreach ($xmlString->league->teams->team as $team) {
            $teamId = (string) $team->teamID;

            if ($teamId === (string) $team_key) {

                if (isset($team->roster->player)) {
                    foreach ($team->roster->player as $player) {
                        $playerFirstName = (string) $player->firstName;
                        $playerLastName = (string) $player->lastName;
                        $playerTeam = (string) $player->nflTeam;
                        $position = (string) $player->positionCode;

                        $currentPlayer = new stdClass();
                        $currentPlayer->player_id = $playerId;
                        $currentPlayer->player_first_name = $playerFirstName;
                        $currentPlayer->player_last_name = $playerLastName;
                        $currentPlayer->player_team = strtoupper($playerTeam);
                        $currentPlayer->position = $position;

                        $roster[] = $currentPlayer;
                    }
                }
                break; // Exit the loop once the matching team is found
            }
        }

		$positionOrder = [
			'QB' => 1,
			'RB' => 2,
			'WR' => 3,
			'TE' => 4
		];
	
		// Sort the roster by the custom position order
		usort($roster, function($a, $b) use ($positionOrder) {
			$posA = $positionOrder[$a->position] ?? PHP_INT_MAX;
			$posB = $positionOrder[$b->position] ?? PHP_INT_MAX;
			return $posA <=> $posB;
		});
	
		return $roster; // Return the processed roster
	}

	function get_draft_picks($league_id, $team_key) {
		$rosterXml = $this->getLeagueRosters($league_id);
		if ($rosterXml === false) {
			return []; // Return an empty array if fetching the roster fails
		}
	
		// Load the roster XML into a SimpleXML object
		$xmlString = @simplexml_load_string($rosterXml);
		if ($xmlString === false) {
			return []; // Return an empty array if XML parsing fails
		}
	
		$draftPicks = [];
	
		foreach ($xmlString->league->teams->team as $team) {
			$teamId = (string) $team->teamID;
	
			if ($teamId === (string) $team_key) {
				if (isset($team->draftPicks->pick)) {
					foreach ($team->draftPicks->pick as $pick) {
						if ($pick->roundPick == '0' || $pick->draftRound > 3) {
							$pick_info = $this->ordinal($pick->draftRound) . ' (Mid)';
						} else {
							$pick_info = $pick->draftRound . '.' . str_pad($pick->roundPick, 2, '0', STR_PAD_LEFT);
						}

						$draftPick = new stdClass();
						$draftPick->year = (int)$pick->draftSeason;
						$draftPick->round = (int)$pick->draftRound;
						$draftPick->pickValue = $pick_info;
	
						if ((string)$pick->draftPickTeamName === (string)$team->teamName) {
							$draftPick->displayText = $pick->draftSeason . ' Draft Pick Round ' . $pick->draftRound;
						} else {
							$draftPick->displayText = $pick->draftSeason . ' Draft Pick Round ' . $pick->draftRound . ' from ' . $pick->draftPickTeamName;
						}
	
						$draftPicks[] = $draftPick;
					}
				}
				break; // Exit the loop once the matching team is found
			}
		}
	
		return $draftPicks;
	}	

	function ordinal($num) {
		// Special case "teenth"
		if ( ($num / 10) % 10 != 1 ) {
			// Handle 1st, 2nd, 3rd
			switch( $num % 10 ) {
				case 1: return $num . 'st';
				case 2: return $num . 'nd';
				case 3: return $num . 'rd';  
			}
		}

		// Everything else is "nth"
		return $num . 'th';
	}

    public function getLeagueSettings($leagueRulesXml)
    {
        $settings = [
            "superflex" => false,
            "2QB" => false,
            "standard" => false,
            "TE_Premium" => false,
            "RB_PPC" => false
        ];

        $leagueRulesData = simplexml_load_string($leagueRulesXml);

        if (isset($leagueRulesData->league->rosterPositions->rosterPosition)) {
            foreach ($leagueRulesData->league->rosterPositions->rosterPosition as $rosterPosition) {
                $positionCategoryCode = (string) $rosterPosition->positionCategoryCode;
                $numStarters = (int) $rosterPosition->numStarters;
        
                if ($positionCategoryCode === 'QB' && $numStarters === 2) {
                    $settings["2QB"] = true;
                } elseif ($positionCategoryCode === 'SUPERFLEX') {
                    $settings["superflex"] = true;
                }
            }
        }
        
        if (!$settings["superflex"] && !$settings["2QB"]) {
            $settings["standard"] = true;
        }
        
        $wrFantasyPoints = [];
        $teFantasyPoints = [];
        
        if (isset($leagueRulesData->league->scoringRule)) {
            foreach ($leagueRulesData->league->scoringRule as $rule) {
                $scoringCategory = trim((string) $rule->scoringCategory);
                $positionCode = trim((string) $rule->positionCode);
        
                if ($scoringCategory === "RECP" && $positionCode === "WR") {
                    if (isset($rule->scoringValue)) {
                        foreach ($rule->scoringValue as $value) {
                            $wrFantasyPoints[] = (float) $value->fantasyPoints;
                        }
                    }
                } elseif ($scoringCategory === "RECP" && $positionCode === "TE") {
                    if (isset($rule->scoringValue)) {
                        foreach ($rule->scoringValue as $value) {
                            $teFantasyPoints[] = (float) $value->fantasyPoints;
                        }
                    }
                }
            }
        }

        if (!empty($wrFantasyPoints) && !empty($teFantasyPoints) && $wrFantasyPoints[0] < $teFantasyPoints[0]) {
            $settings["TE_Premium"] = true;
        }

        $settings["RB_PPC"] = false;

        return $settings;
    }

    function checkIfLeagueIsPPR($leagueRulesXml): string {
        // Load the XML string into a SimpleXML object
        $leagueRulesData = simplexml_load_string($leagueRulesXml);
    
        // Check if scoringRules exist
        if (isset($leagueRulesData->league->scoringRules->scoringRule)) {
            // Loop through all scoringRule elements within scoringRules
            foreach ($leagueRulesData->league->scoringRules->scoringRule as $rule) {
                $scoringCategory = trim((string) $rule->scoringCategory);
                $positionCode = trim((string) $rule->positionCode);
    
                // Check for "RECP" and "WR"
                if ($scoringCategory === "RECP" && $positionCode === "WR") {
                    $fantasyPoints = [];
    
                    // Check if scoringValue exists and extract fantasyPoints
                    if (isset($rule->scoringValue)) {
                        foreach ($rule->scoringValue as $value) {
                            $fantasyPoints[] = (float) $value->fantasyPoints;
                        }
                    }
    
                    // Determine PPR type based on fantasyPoints
                    if (empty($fantasyPoints)) {
                        return "Non-PPR";
                    } elseif ($fantasyPoints[0] === 0.0) {
                        return "Non-PPR";
                    } elseif ($fantasyPoints[0] === 0.5) {
                        return ".5 PPR";
                    } elseif ($fantasyPoints[0] >= 1.0) {
                        return "PPR";
                    }
                }
            }
        }
    
        // Return "Unknown" if no matching rule is found
        return "Unknown";
    }

    function getLeagueStatus($leagueId) {
        // Get draft results XML and parse it
        $draftResultsXml = $this->getLeagueDraftResults($leagueId, $this->season);
        $draftResultsData = simplexml_load_string($draftResultsXml);
    
        // Check if there are picks
        if (isset($draftResultsData->draft->pick)) {
            $firstPick = $draftResultsData->pick[0];
            if (!isset($firstPick->player)) {
                return "pre_draft";
            }
        }
    
        // Get league matchups XML and parse it
        $leagueMatchupsXml = $this->getLeagueMatchups($leagueId, $this->season);
        $leagueMatchupsData = simplexml_load_string($leagueMatchupsXml);
    
        // Find all matchups
        $matchups = $leagueMatchupsData->matchups->matchup ?? [];
    
        // Find the maximum leagueWeek
        $maxLeagueWeek = 0;
        foreach ($matchups as $matchup) {
            $leagueWeek = (int) $matchup['leagueWeek'];
            if ($leagueWeek > $maxLeagueWeek) {
                $maxLeagueWeek = $leagueWeek;
            }
        }
    
        // Find the first matchup with the maximum leagueWeek
        $firstMaxLeagueWeek = null;
        foreach ($matchups as $matchup) {
            if ((int) $matchup['leagueWeek'] === $maxLeagueWeek) {
                $firstMaxLeagueWeek = $matchup;
                break;
            }
        }
    
        if ($firstMaxLeagueWeek) {
            $homeTeamScore = (float) $firstMaxLeagueWeek['homeTeamScore'];
            $awayTeamScore = (float) $firstMaxLeagueWeek['awayTeamScore'];
    
            if ($homeTeamScore !== 0 || $awayTeamScore !== 0) {
                $nflState = $this->getNFLState("nfl");
                if ($maxLeagueWeek < $nflState['week']) {
                    return "complete";
                } else {
                    return "in_season";
                }
            }
        }
    
        // Default response
        return "pre_draft";
    }

    public function getRosterPositions($leagueRulesXml)
    {
        $positionList = [];

        // Load the XML data
        $leagueRulesData = simplexml_load_string($leagueRulesXml);

        // Get the number of bench players
        $benchPlayers = (int) $leagueRulesData->league->benchPlayers;

        // Get all roster positions
        if (isset($leagueRulesData->league->rosterPositions->rosterPosition)) {
            foreach ($leagueRulesData->league->rosterPositions->rosterPosition as $position) {
                $positionCategoryCode = (string) $position->positionCategoryCode;
                $numStarters = (int) $position->numStarters;

                // Add the position to the list as many times as there are starters
                for ($i = 0; $i < $numStarters; $i++) {
                    $positionList[] = $positionCategoryCode;
                }
            }
        }

        // Add bench players to the list
        for ($i = 0; $i < $benchPlayers; $i++) {
            $positionList[] = 'BN';
        }

        return $positionList;
    }
    
    


    



	/****************************************************************************************
	 * 
	 * 
	 *              FFPC Web API Functions
	 *              (Retrieving the data from FFPC)
	 * 
	 * 
	 ****************************************************************************************/
    public function getAllLeagues($leagueTypeID = null, $Season = null, $draftStartDateFrom = null, $draftStartDateTo = null, $entryFee = null, $leagueAllocationMethodID = null, $leagueGroupID = null)
    {
        $params = [];

        if (!is_null($leagueTypeID)) {
            $params['leagueTypeID'] = $leagueTypeID;
        }
        if (!is_null($Season)) {
            $params['Season'] = $Season;
        }
        if (!is_null($draftStartDateFrom)) {
            $params['draftStartDateFrom'] = $draftStartDateFrom;
        }
        if (!is_null($draftStartDateTo)) {
            $params['draftStartDateTo'] = $draftStartDateTo;
        }
        if (!is_null($entryFee)) {
            $params['entryFee'] = $entryFee;
        }
        if (!is_null($leagueAllocationMethodID)) {
            $params['leagueAllocationMethodID'] = $leagueAllocationMethodID;
        }
        if (!is_null($leagueGroupID)) {
            $params['leagueGroupID'] = $leagueGroupID;
        }

        $queryString = http_build_query($params);
        $url = $this->api_url . "FFPCListLeagues.ashx?" . $queryString;

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
            curl_close($ch);
            return null;
        }

        curl_close($ch);

        echo $response ? $response : "Failed to retrieve data.";
    }


    public function getPlayers($player_status)
    {
        $url = $this->api_url . "ffpcactiveplayers.ashx?players=" . urlencode($player_status);

        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);

        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
            curl_close($ch);
            return null;
        }

        curl_close($ch);

        echo $response ? $response : "Failed to retrieve data.";
    }


    public function getUserLeagues($username = null, $email = null, $Season = null)
    {
        $params = [];

        if (!is_null($username)) {
            $params['username'] = $username;
        }
        if (!is_null($email)) {
            $params['email'] = $email;
        }
        if (!is_null($Season)) {
            $params['Season'] = $Season;
        }

        $queryString = http_build_query($params);
        $url = $this->endpoint . "FFPCUsersLeagues.ashx?" . $queryString;

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute the request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
            curl_close($ch);
            return null;
        }

        // Close the cURL session
        curl_close($ch);

        // Return the response or handle it
        return $response ? $response : "Failed to retrieve User League data.";
    }

    public function getLeagueRules($league_id, $Season = null)
    {
        $params = ['leagueid' => $league_id];

        if (!is_null($Season)) {
            $params['Season'] = $Season;
        }

        $queryString = http_build_query($params);
        $url = $this->api_url . "FFPCLeagueInfo.ashx?" . $queryString;

        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute the request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
            curl_close($ch);
            return null;
        }

        // Close the cURL session
        curl_close($ch);

        // Return the response or handle it
        return $response ? $response : "Failed to retrieve data.";
    }


    public function getLeagueRosters($league_id)
    {
        $url = $this->api_url . "FFPCLeagueRosters.ashx?leagueid=" . $league_id;

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FAILONERROR => true,
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            echo "Failed to retrieve data: " . curl_error($curl);
        }

        curl_close($curl);

        return $response ? $response : null;
    }


    public function getLeagueFreeAgents($league_id)
    {
        $url = $this->api_url . "FFPCLeagueFreeAgents.ashx?leagueid=" . $league_id;

        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FAILONERROR => true,
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            echo "Failed to retrieve data: " . curl_error($curl);
        }

        curl_close($curl);

        return $response ? $response : null;
    }

    public function getLeagueMatchups($league_id, $Season = null)
    {
        $params = ['leagueid' => $league_id];

        if (!is_null($Season)) {
            $params['Season'] = $Season;
        }

        $queryString = http_build_query($params);
        $url = $this->api_url . "FFPCLeagueMatchups.ashx?" . $queryString;

        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute the request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
            curl_close($ch);
            return null;
        }

        // Close the cURL session
        curl_close($ch);

        // Return the response or handle it
        return $response ? $response : "Failed to retrieve data.";
    }

    public function getLeagueWeekPoints($league_id, $weekNum = null)
    {
        $params = "";

        if (!is_null($weekNum)) {
            $params .= "&weekNum=" . $weekNum;
        }

        $response = file_get_contents($this->api_url . "FFPCLeagueWeekPoints.ashx?leagueid=" . $league_id . $params);
        if ($response) {
            return $response;
        } else {
            echo "Failed to retrieve data.";
        }
    }

    public function getLeagueStandings($league_id, $weekNum = null, $Season = null)
    {
        $params = "";

        if (!is_null($Season)) {
            $params .= "&Season=" . $Season;
        }
        if (!is_null($weekNum)) {
            $params .= "&weekNum=" . $weekNum;
        }

        $response = file_get_contents($this->api_url . "FFPCLeagueStandings.ashx?leagueid=" . $league_id . $params);
        if ($response) {
            return $response;
        } else {
            echo "Failed to retrieve data.";
        }
    }

    public function getLeagueDraftResults($league_id, $Season = null)
    {
        $params = ['leagueid' => $league_id];

        if (!is_null($Season)) {
            $params['Season'] = $Season;
        }

        $queryString = http_build_query($params);
        $url = $this->api_url . "FFPCLeagueDraftPicks.ashx?" . $queryString;

        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        // Execute the request
        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
            curl_close($ch);
            return null;
        }

        // Close the cURL session
        curl_close($ch);

        // Return the response or handle it
        return $response ? $response : "Failed to retrieve data.";
    }

    // GETS THE NFL STATE FROM THE SLEEPER API
    public function getNFLState($sport)
    {
        $settings = get_option('dtc_settings');	
		$api_url =$settings['sleeper_api_url'];

        $url = $api_url . "state/" . $sport;

        $ch = curl_init();

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);

        $response = curl_exec($ch);

        // Check for errors
        if (curl_errno($ch)) {
            echo "cURL Error: " . curl_error($ch);
            curl_close($ch);
            return null;
        }

        curl_close($ch);

        // Convert the JSON response to stdClass
        $data = json_decode($response);

        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "JSON Error: " . json_last_error_msg();
            return null;
        }

        return $data;
    }

}