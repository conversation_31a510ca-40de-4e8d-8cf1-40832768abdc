<?php

// autoload_static.php @generated by Composer

namespace Composer\Autoload;

class ComposerStaticInit46b548fa743eb9570cbbdd53746b295c
{
    public static $prefixLengthsPsr4 = array (
        'E' => 
        array (
            'ExtPHP\\XmlToJson\\' => 17,
        ),
    );

    public static $prefixDirsPsr4 = array (
        'ExtPHP\\XmlToJson\\' => 
        array (
            0 => __DIR__ . '/..' . '/extphp/xml-to-json/src',
        ),
    );

    public static $classMap = array (
        'Composer\\InstalledVersions' => __DIR__ . '/..' . '/composer/InstalledVersions.php',
    );

    public static function getInitializer(ClassLoader $loader)
    {
        return \Closure::bind(function () use ($loader) {
            $loader->prefixLengthsPsr4 = ComposerStaticInit46b548fa743eb9570cbbdd53746b295c::$prefixLengthsPsr4;
            $loader->prefixDirsPsr4 = ComposerStaticInit46b548fa743eb9570cbbdd53746b295c::$prefixDirsPsr4;
            $loader->classMap = ComposerStaticInit46b548fa743eb9570cbbdd53746b295c::$classMap;

        }, null, ClassLoader::class);
    }
}
