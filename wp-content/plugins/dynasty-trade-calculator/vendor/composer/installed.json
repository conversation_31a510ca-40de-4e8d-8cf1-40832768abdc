{"packages": [{"name": "extphp/xml-to-json", "version": "v0.2.0", "version_normalized": "*******", "source": {"type": "git", "url": "https://github.com/extphp/xml-to-json.git", "reference": "8160790416c685ee9e618cd5f3951387aea2a6f8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/extphp/xml-to-json/zipball/8160790416c685ee9e618cd5f3951387aea2a6f8", "reference": "8160790416c685ee9e618cd5f3951387aea2a6f8", "shasum": ""}, "require-dev": {"phpunit/phpunit": ">=5.0"}, "time": "2019-08-21T08:29:48+00:00", "type": "library", "installation-source": "dist", "autoload": {"psr-4": {"ExtPHP\\XmlToJson\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An XML to JSON converter that will properly preserve attributes.", "keywords": ["converter", "json", "xml"], "support": {"issues": "https://github.com/extphp/xml-to-json/issues", "source": "https://github.com/extphp/xml-to-json/tree/v0.2.0"}, "install-path": "../extphp/xml-to-json"}], "dev": true, "dev-package-names": []}